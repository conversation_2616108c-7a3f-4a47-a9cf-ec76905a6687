#ifndef FITMANAGER_H
#define FITMANAGER_H

#include "FitModelInterface.h"
#include "FitModels.h"
#include "FitParameterModel.h"
#include "../SharedDataManager.h"
#include <QObject>
#include <QMap>
#include <QVector>
#include <QString>
#include <QSharedPointer>
#include <QColor>

// 拟合管理器类
class FitManager : public QObject {
    Q_OBJECT

public:
    static FitManager* getInstance();
    ~FitManager();

    // 获取可用的拟合模型列表
    QStringList getAvailableModels() const;

    // 获取指定类型的拟合模型
    QSharedPointer<FitModelInterface> getModel(const QString& modelName);

    // 执行拟合并将结果直接添加到SharedDataManager
    FitResult performFitAndStore(
        const QString& modelName,
        TabType tab,
        PlotDataType plotType,
        const QVector<double>& x,
        const QVector<double>& y,
        double xMin = 0,
        double xMax = 0
        );

    // 只执行拟合计算，不存储结果
    FitResult performFit(
        const QString& modelName,
        const QVector<double>& x,
        const QVector<double>& y,
        double xMin = 0,
        double xMax = 0
        );

    // 执行卷积拟合，不通过尾部拟合获取初始参数
    FitResult performConvolutionFit(
        const QString& modelName,
        const QVector<double>& x,
        const QVector<double>& y,
        double xMin = 0,
        double xMax = 0
        );

signals:
    // 拟合完成信号
    void fitCompleted(PlotDataType plotType, const FitResult& result);

private slots:
    // 当模型参数数量变化时更新N指数衰减模型
    void updateNExponentialModel(int count);

private:
    FitManager(QObject* parent = nullptr);
    static FitManager* instance;

    // 注册所有可用的拟合模型
    void registerModels();

    // 存储所有可用的拟合模型
    QMap<QString, QSharedPointer<FitModelInterface>> m_models;
};

#endif // FITMANAGER_H
