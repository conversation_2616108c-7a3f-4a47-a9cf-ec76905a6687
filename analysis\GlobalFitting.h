#ifndef GLOBALFITTING_H
#define GLOBALFITTING_H

#include <QVector>
#include <QString>
#include <QMap>
#include <QDebug>
#include <Eigen/Dense>
#include "FitModelInterface.h"
#include "FitModels.h"

// 全局拟合结果结构
struct GlobalFitResult {
    bool success = false;
    QString errorMessage;

    // 共享参数：所有曲线的衰减常数
    QMap<QString, double> sharedParameters; // τ₁, τ₂, τ₃, ...

    // 组特异性参数：每组曲线的振幅和基线
    QVector<QMap<QString, double>> groupSpecificParameters; // A₁, A₂, A₃, B for each curve

    // 拟合曲线数据（每条曲线一组）
    QVector<QVector<double>> xData;
    QVector<QVector<double>> yData;

    // 拟合质量指标
    double globalChiSquare = 0.0;
    double globalRSquare = 0.0;
    QVector<double> individualChiSquare;
    QVector<double> individualRSquare;

    // 算法信息
    double fitMethod = 1.0; // 1.0=LeastSquares, 2.0=MaximumLikelihood, 3.0=BayesianAnalysis
    double logLikelihood = 0.0; // 仅用于MLE和贝叶斯分析

    // 贝叶斯分析的参数不确定度（仅用于贝叶斯分析）
    QMap<QString, double> sharedParameterStdDevs;
    QVector<QMap<QString, double>> groupParameterStdDevs;
};

// 全局拟合输入数据结构
struct GlobalFitData {
    QVector<QVector<double>> xData; // 每条曲线的x数据
    QVector<QVector<double>> yData; // 每条曲线的y数据
    QVector<QString> curveNames;    // 曲线名称
    double xMin = 0.0;              // 拟合范围起始
    double xMax = 0.0;              // 拟合范围结束
};

// 全局拟合类
class GlobalFitting {
public:
    // 拟合算法枚举
    enum Algorithm {
        LeastSquares = 0,
        MaximumLikelihood = 1,
        BayesianAnalysis = 2
    };

    GlobalFitting();
    ~GlobalFitting();

    // 设置拟合参数
    void setExponentialCount(int n);
    void setAlgorithm(Algorithm algorithm);
    void setIterations(int iterations);

    // 执行全局拟合
    GlobalFitResult performGlobalFit(const GlobalFitData& data);

private:
    // 拟合参数
    int m_n = 1;                    // 指数项数量
    Algorithm m_algorithm = LeastSquares;
    int m_iterations = 1000;

    // 内部拟合函数
    GlobalFitResult fitGlobalLeastSquares(const GlobalFitData& data);
    GlobalFitResult fitGlobalMaximumLikelihood(const GlobalFitData& data);
    GlobalFitResult fitGlobalBayesian(const GlobalFitData& data);

    // 辅助函数
    Eigen::VectorXd getInitialParameters(const GlobalFitData& data);
    void extractParameters(const Eigen::VectorXd& params,
                          QMap<QString, double>& sharedParams,
                          QVector<QMap<QString, double>>& groupParams,
                          int numCurves);
    QVector<double> calculateGlobalModel(const QVector<double>& x,
                                       const QMap<QString, double>& sharedParams,
                                       const QMap<QString, double>& groupParams);
    double calculateGlobalChiSquare(const GlobalFitData& data,
                                   const QMap<QString, double>& sharedParams,
                                   const QVector<QMap<QString, double>>& groupParams);
    double calculateGlobalRSquared(const GlobalFitData& data,
                                  const QMap<QString, double>& sharedParams,
                                  const QVector<QMap<QString, double>>& groupParams);

    // 贝叶斯分析相关方法
    QVector<Eigen::VectorXd> globalMetropolisHastings(const GlobalFitData& data,
                                                      const Eigen::VectorXd& initialParams,
                                                      const Eigen::VectorXd& priorMeans,
                                                      const Eigen::VectorXd& priorStdDevs,
                                                      int numSamples, int burnIn);
    double calculateGlobalLogLikelihood(const GlobalFitData& data, const Eigen::VectorXd& params);
    double calculateGlobalLogPrior(const Eigen::VectorXd& params,
                                  const Eigen::VectorXd& priorMeans,
                                  const Eigen::VectorXd& priorStdDevs);
};

#endif // GLOBALFITTING_H
