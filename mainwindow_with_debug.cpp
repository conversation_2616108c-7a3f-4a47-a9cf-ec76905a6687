#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QVector>
#include <cmath>
#include <qcustomplot.h> // 确保包含该头文件
#include <QMessageBox>
#include <QMenu>
#include <QFileDialog>
#include <QTextStream>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    // 启用所有plot的上下文菜单
    ui->plot1->setContextMenuPolicy(Qt::CustomContextMenu);
    ui->plot2->setContextMenuPolicy(Qt::CustomContextMenu);
    ui->plot3->setContextMenuPolicy(Qt::CustomContextMenu);

    // 连接上下文菜单信号
    connect(ui->plot1, &QCustomPlot::customContextMenuRequested,
            this, &MainWindow::on_plot1_contextMenuRequested);
    connect(ui->plot2, &QCustomPlot::customContextMenuRequested,
            this, &MainWindow::on_plot2_contextMenuRequested);
    connect(ui->plot3, &QCustomPlot::customContextMenuRequested,
            this, &MainWindow::on_plot3_contextMenuRequested);

    // 连接组件数量变化信号
    connect(ui->componentsComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &MainWindow::on_componentsComboBox_currentIndexChanged);

    // 连接应用参数按钮信号
    connect(ui->applyParamsButton, &QPushButton::clicked,
            this, &MainWindow::on_applyParamsButton_clicked);

    // 初始化数据向量
    t.resize(4096);
    decay.resize(4096);
    gauss.resize(4096);
    convResult.resize(4096);

    // 初始化图表
    ui->plot1->addGraph();
    ui->plot2->addGraph();
    ui->plot3->addGraph();

    // 设置默认组件数量
    ui->componentsComboBox->setCurrentIndex(1); // 默认选择双指数衰减
    updateComponentVisibility();

    // 注意：不在构造函数中生成数据和绘制图像
    // 只有在点击"应用参数"按钮后才会生成数据和绘制图像
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::on_Create_Button_clicked()
{
    // 检查是否已经生成了数据
    if (ui->plot1->graph(0)->dataCount() == 0) {
        QMessageBox::warning(this, "警告", "请先点击'应用参数'按钮生成数据！");
        return;
    }

    static bool isLinear = true; // 状态跟踪变量

    // 切换所有plot的坐标轴类型
    if(isLinear) {
        // 设置为对数坐标
        ui->plot1->yAxis->setScaleType(QCPAxis::stLogarithmic);
        ui->plot2->yAxis->setScaleType(QCPAxis::stLogarithmic);
        ui->plot3->yAxis->setScaleType(QCPAxis::stLogarithmic);

        QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
        logTicker->setLogBase(10);  // 设置底数为10
        logTicker->setSubTickCount(9);  // 设置子刻度数量

        // 设置科学计数法格式
        ui->plot1->yAxis->setNumberFormat("eb");
        ui->plot1->yAxis->setTicker(logTicker);
        ui->plot1->yAxis->setNumberPrecision(0);

        ui->plot2->yAxis->setNumberFormat("eb");
        ui->plot2->yAxis->setTicker(logTicker);
        ui->plot2->yAxis->setNumberPrecision(0);

        ui->plot3->yAxis->setNumberFormat("eb");
        ui->plot3->yAxis->setTicker(logTicker);
        ui->plot3->yAxis->setNumberPrecision(0);

    } else {
        // 恢复线性坐标
        ui->plot1->yAxis->setScaleType(QCPAxis::stLinear);
        ui->plot2->yAxis->setScaleType(QCPAxis::stLinear);
        ui->plot3->yAxis->setScaleType(QCPAxis::stLinear);

        // 重置为默认的线性刻度器
        QSharedPointer<QCPAxisTicker> linearTicker(new QCPAxisTicker);

        ui->plot1->yAxis->setTicker(linearTicker);
        ui->plot1->yAxis->setNumberFormat("g");
        ui->plot1->yAxis->setNumberPrecision(6);

        ui->plot2->yAxis->setTicker(linearTicker);
        ui->plot2->yAxis->setNumberFormat("g");
        ui->plot2->yAxis->setNumberPrecision(6);

        ui->plot3->yAxis->setTicker(linearTicker);
        ui->plot3->yAxis->setNumberFormat("g");
        ui->plot3->yAxis->setNumberPrecision(6);
    }

    ui->plot1->yAxis->setRange(5, plot1max);
    ui->plot2->yAxis->setRange(5, plot2max);
    ui->plot3->yAxis->setRange(5, plot3max);

    // 刷新所有绘图
    ui->plot1->replot();
    ui->plot2->replot();
    ui->plot3->replot();

    isLinear = !isLinear; // 切换状态
}

void MainWindow::on_Change_Button_clicked()
{
    // 检查是否已经生成了数据
    if (ui->plot1->graph(0)->dataCount() == 0) {
        QMessageBox::warning(this, "警告", "请先点击'应用参数'按钮生成数据！");
        return;
    }

    // 获取plot2和plot3的数据
    QSharedPointer<QCPGraphDataContainer> plot2Data = ui->plot2->graph(0)->data();
    QSharedPointer<QCPGraphDataContainer> plot3Data = ui->plot3->graph(0)->data();

    // 计算plot3的曲线积分(梯形法)
    double integralPlot3 = 0.0;
    for(int i = 1; i < plot3Data->size(); ++i) {
        double dx = plot3Data->at(i)->key - plot3Data->at(i-1)->key;
        double avgY = (plot3Data->at(i)->value + plot3Data->at(i-1)->value) / 2.0;
        integralPlot3 += avgY * dx;
    }

    // 计算plot2的曲线积分(梯形法)
    double integralPlot2 = 0.0;
    for(int i = 1; i < plot2Data->size(); ++i) {
        double dx = plot2Data->at(i)->key - plot2Data->at(i-1)->key;
        double avgY = (plot2Data->at(i)->value + plot2Data->at(i-1)->value) / 2.0;
        integralPlot2 += avgY * dx;
    }

    // 计算差值
    double finalResult = integralPlot3 - integralPlot2;

    // 显示结果
    QMessageBox::information(this, "积分计算结果", 
        QString("最终结果: %1\n"
                "plot3积分值: %2\n"
                "plot2积分值: %3")
        .arg(finalResult)
        .arg(integralPlot3)
        .arg(integralPlot2));
}

void MainWindow::on_Fit_Button_clicked()
{
    // 检查是否已经生成了数据
    if (ui->plot1->graph(0)->dataCount() == 0) {
        QMessageBox::warning(this, "警告", "请先点击'应用参数'按钮生成数据！");
        return;
    }

    // 横坐标转换：t[i] * 50 / 1000 = t[i] * 0.05
    for(auto& plot : {ui->plot1, ui->plot2, ui->plot3}) {
        // 获取原始数据
        QSharedPointer<QCPGraphDataContainer> data = plot->graph(0)->data();

        // 创建转换后的横坐标向量和纵坐标向量
        QVector<double> t_ns(data->size());
        QVector<double> yValues(data->size());
        for(int i=0; i<data->size(); ++i) {
            t_ns[i] = data->at(i)->key * 0.05; // 转换为纳秒
            yValues[i] = data->at(i)->value;   // 直接访问value成员
        }

        // 设置新的坐标数据
        plot->graph(0)->setData(t_ns, yValues);

        // 设置坐标轴标签和范围
        plot->xAxis->setLabel("Time (ns)");
        plot->xAxis->setRange(0, 204.8);
        plot->yAxis->rescale();
        plot->replot();
    }

    // 通过坐标轴缩放计算τ值
    QString result;

    // 根据组件数量显示不同的标题
    if (componentCount == 1) {
        result = QString("单指数衰减模型:\n");
    } else if (componentCount == 2) {
        result = QString("双指数衰减模型:\n");
    } else if (componentCount == 3) {
        result = QString("三指数衰减模型:\n");
    }

    // 显示所有组件的参数
    for (int i = 0; i < componentCount; i++) {
        double tau_scale = tauValues[i] * 0.05; // τ值对应的纳秒时间
        result += QString("A%1 = %2, τ%1 = %3 ns\n")
                  .arg(i+1, 0, 10)
                  .arg(amplitudeValues[i])
                  .arg(tau_scale, 0, 'f', 1);
    }

    QMessageBox::information(this, "τ值计算结果", result);
}

// 处理组件数量变化
void MainWindow::on_componentsComboBox_currentIndexChanged(int index)
{
    // 组件数量从1开始，而索引从0开始
    componentCount = index + 1;
    updateComponentVisibility();
}

// 更新组件可见性
void MainWindow::updateComponentVisibility()
{
    // 根据组件数量显示/隐藏相应的输入框
    ui->component1GroupBox->setVisible(componentCount >= 1);
    ui->component2GroupBox->setVisible(componentCount >= 2);
    ui->component3GroupBox->setVisible(componentCount >= 3);
}

// 应用参数按钮点击事件
void MainWindow::on_applyParamsButton_clicked()
{
    // 从UI获取参数值
    if (componentCount >= 1) {
        amplitudeValues[0] = ui->a1SpinBox->value();
        tauValues[0] = ui->tau1SpinBox->value();
    }

    if (componentCount >= 2) {
        amplitudeValues[1] = ui->a2SpinBox->value();
        tauValues[1] = ui->tau2SpinBox->value();
    }

    if (componentCount >= 3) {
        amplitudeValues[2] = ui->a3SpinBox->value();
        tauValues[2] = ui->tau3SpinBox->value();
    }

    // 重新生成衰减曲线并更新图表
    generateDecayCurve();
}

// 生成衰减曲线并更新图表
void MainWindow::generateDecayCurve()
{
    qDebug() << "=== 开始生成衰减曲线和IRF ===";

    // 生成指数衰减曲线
    for(int i=0; i<4096; ++i){
        t[i] = i;
        // 使用类成员变量的指数衰减模型
        double baseValue = 0.0;

        // 根据组件数量计算衰减值
        for(int j=0; j<componentCount; j++) {
            baseValue += amplitudeValues[j] * exp(-i/tauValues[j]);
        }

        decay[i] = baseValue;
    }

    // 添加衰减曲线的调试信息
    double decaySum = 0.0, decayMax = 0.0, decayMin = decay[0];
    for(int i = 0; i < 4096; ++i) {
        decaySum += decay[i];
        if(decay[i] > decayMax) decayMax = decay[i];
        if(decay[i] < decayMin) decayMin = decay[i];
    }
    qDebug() << "=== 衰减曲线调试信息 ===";
    qDebug() << "衰减曲线 - sum:" << decaySum << ", max:" << decayMax << ", min:" << decayMin;
    qDebug() << "衰减曲线 - 组件数量:" << componentCount;
    for(int j = 0; j < componentCount; j++) {
        qDebug() << QString("  组件%1 - A%2=%3, τ%4=%5").arg(j+1).arg(j+1).arg(amplitudeValues[j]).arg(j+1).arg(tauValues[j]);
    }

    // 生成伪高斯仪器响应函数（如果尚未生成）
    if (gauss[0] == 0) {
        const double mu = 948.0;       // 中心位置
        const double sigma_left = 0.3;  // 左侧展宽
        const double sigma_right = 0.15; // 右侧展宽

        for(int i=0; i<4096; ++i){
            double x = t[i] - mu;
            double sigma = (x < 0) ? sigma_left : sigma_right;
            double gauss_base = exp(-x*x/(2*sigma*sigma));

            gauss[i] = gauss_base / (sigma * sqrt(2*M_PI));
        }

        // 添加模拟IRF的调试信息
        double gaussSum = 0.0, gaussMax = 0.0, gaussMin = gauss[0];
        for(int i = 0; i < 4096; ++i) {
            gaussSum += gauss[i];
            if(gauss[i] > gaussMax) gaussMax = gauss[i];
            if(gauss[i] < gaussMin) gaussMin = gauss[i];
        }
        qDebug() << "=== 模拟生成IRF调试信息 ===";
        qDebug() << "模拟IRF - sum:" << gaussSum << ", max:" << gaussMax << ", min:" << gaussMin;
        qDebug() << "模拟IRF - 数据类型: double, 数据点数:" << 4096;
        qDebug() << "模拟IRF - 中心位置:" << mu << ", 左侧σ:" << sigma_left << ", 右侧σ:" << sigma_right;
        qDebug() << "模拟IRF - 峰值位置索引:" << static_cast<int>(mu);

        // 计算IRF的有效宽度（FWHM近似）
        double halfMax = gaussMax / 2.0;
        int leftHalf = -1, rightHalf = -1;
        for(int i = 0; i < 4096; ++i) {
            if(leftHalf == -1 && gauss[i] >= halfMax) leftHalf = i;
            if(gauss[i] >= halfMax) rightHalf = i;
        }
        if(leftHalf != -1 && rightHalf != -1) {
            qDebug() << "模拟IRF - FWHM近似宽度:" << (rightHalf - leftHalf) << "个数据点";
        }
    }

    // 重新计算卷积
    for(int n=0; n<4096; ++n){
        convResult[n] = 0;
        for(int m=0; m<=n; ++m){
            convResult[n] += decay[m] * gauss[n-m];
        }
        convResult[n] *= 1.0; // 步长简化为1
    }

    // 添加卷积结果的调试信息
    double convSum = 0.0, convMax = 0.0, convMin = convResult[0];
    for(int i = 0; i < 4096; ++i) {
        convSum += convResult[i];
        if(convResult[i] > convMax) convMax = convResult[i];
        if(convResult[i] < convMin) convMin = convResult[i];
    }
    qDebug() << "=== 卷积结果调试信息 ===";
    qDebug() << "卷积结果 - sum:" << convSum << ", max:" << convMax << ", min:" << convMin;
    qDebug() << "卷积效应 - 原始衰减max:" << decayMax << ", 卷积后max:" << convMax;
    qDebug() << "卷积效应 - max比值:" << (decayMax > 0 ? convMax/decayMax : 0);
    qDebug() << "卷积效应 - sum比值:" << (decaySum > 0 ? convSum/decaySum : 0);

    // 图表已在构造函数中初始化，无需再次初始化

    // 更新图表
    ui->plot1->graph(0)->setData(t, decay);
    ui->plot1->rescaleAxes();
    ui->plot1->replot();

    ui->plot2->graph(0)->setData(t, gauss);
    ui->plot2->rescaleAxes();
    ui->plot2->replot();

    ui->plot3->graph(0)->setData(t, convResult);
    ui->plot3->rescaleAxes();
    ui->plot3->replot();

    // 更新最大值
    plot1max = decay[0];
    plot2max = gauss[0];
    plot3max = convResult[0];

    for(int i = 0; i<4096; i++)
    {
        if(decay[i] > plot1max) {
            plot1max = decay[i];
        }

        if(gauss[i] > plot2max) {
            plot2max = gauss[i];
        }

        if(convResult[i] > plot3max) {
            plot3max = convResult[i];
        }
    }

    qDebug() << "=== 图表更新完成 ===";
    qDebug() << "plot1 max:" << plot1max << ", plot2 max:" << plot2max << ", plot3 max:" << plot3max;
}

// 上下文菜单处理函数
void MainWindow::on_plot1_contextMenuRequested(const QPoint &pos)
{
    QMenu menu;
    QAction *saveAction = menu.addAction("保存数据");
    connect(saveAction, &QAction::triggered, this, &MainWindow::savePlot1Data);
    menu.exec(ui->plot1->mapToGlobal(pos));
}

void MainWindow::on_plot2_contextMenuRequested(const QPoint &pos)
{
    QMenu menu;
    QAction *saveAction = menu.addAction("保存数据");
    connect(saveAction, &QAction::triggered, this, &MainWindow::savePlot2Data);
    menu.exec(ui->plot2->mapToGlobal(pos));
}

void MainWindow::on_plot3_contextMenuRequested(const QPoint &pos)
{
    QMenu menu;
    QAction *saveAction = menu.addAction("保存数据");
    connect(saveAction, &QAction::triggered, this, &MainWindow::savePlot3Data);
    menu.exec(ui->plot3->mapToGlobal(pos));
}

// 保存数据函数
void MainWindow::savePlot1Data()
{
    QString fileName = QFileDialog::getSaveFileName(this, "保存数据", "", "文本文件 (*.txt)");
    if(fileName.isEmpty()) return;

    QFile file(fileName);
    if(file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        auto data = ui->plot1->graph(0)->data();

        qDebug() << "=== 保存Plot1数据调试信息 ===";
        qDebug() << "保存文件:" << fileName;
        qDebug() << "数据点数:" << data->size();
        qDebug() << "保存格式: 128行重复的int型数据";

        // 生成128行重复数据
        for(int row = 0; row < 128; ++row) {
            for(int i = 0; i < data->size(); ++i) {
                out << static_cast<int>(data->at(i)->value);  // 转换为int型
                if(i != data->size()-1) out << " ";
            }
            out << "\n";
        }
        file.close();

        qDebug() << "Plot1数据保存完成，原始double值被转换为int型";
    }
}

void MainWindow::savePlot2Data()
{
    QString fileName = QFileDialog::getSaveFileName(this, "保存数据", "", "文本文件 (*.txt)");
    if(fileName.isEmpty()) return;

    QFile file(fileName);
    if(file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        auto data = ui->plot2->graph(0)->data();

        // 计算保存前的统计信息
        double saveSum = 0.0, saveMax = 0.0, saveMin = data->at(0)->value;
        for(int i = 0; i < data->size(); ++i) {
            double value = data->at(i)->value;
            saveSum += value;
            if(value > saveMax) saveMax = value;
            if(value < saveMin) saveMin = value;
        }

        qDebug() << "=== 保存Plot2(IRF)数据调试信息 ===";
        qDebug() << "保存文件:" << fileName;
        qDebug() << "数据点数:" << data->size();
        qDebug() << "保存格式: 每行一个double值";
        qDebug() << "保存前统计 - sum:" << saveSum << ", max:" << saveMax << ", min:" << saveMin;
        qDebug() << "数据类型: 保持double精度";

        for(int i = 0; i < data->size(); ++i) {
            out << data->at(i)->value << "\n";  // 保持double精度
        }
        file.close();

        qDebug() << "Plot2(IRF)数据保存完成，保持原始double精度";
    }
}

void MainWindow::savePlot3Data()
{
    QString fileName = QFileDialog::getSaveFileName(this, "保存数据", "", "文本文件 (*.txt)");
    if(fileName.isEmpty()) return;

    QFile file(fileName);
    if(file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        auto data = ui->plot3->graph(0)->data();

        qDebug() << "=== 保存Plot3数据调试信息 ===";
        qDebug() << "保存文件:" << fileName;
        qDebug() << "数据点数:" << data->size();
        qDebug() << "保存格式: 128行重复的int型数据";

        // 生成128行重复数据
        for(int row = 0; row < 128; ++row) {
            for(int i = 0; i < data->size(); ++i) {
                out << static_cast<int>(data->at(i)->value);  // 转换为int型
                if(i != data->size()-1) out << " ";
            }
            out << "\n";
        }
        file.close();

        qDebug() << "Plot3数据保存完成，原始double值被转换为int型";
    }
}

// 添加一个函数来模拟导入IRF数据并进行调试
void MainWindow::loadAndDebugIRFData(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开IRF文件:" << filePath;
        return;
    }

    QTextStream in(&file);
    QVector<double> importedIRF;

    qDebug() << "=== 导入IRF数据调试信息 ===";
    qDebug() << "导入文件:" << filePath;

    // 读取文件数据
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (!line.isEmpty()) {
            bool ok;
            double value = line.toDouble(&ok);
            if (ok) {
                importedIRF.append(value);
            }
        }
    }
    file.close();

    if (!importedIRF.isEmpty()) {
        // 计算导入IRF的统计信息
        double irfSum = 0.0, irfMax = 0.0, irfMin = importedIRF[0];
        for (double val : importedIRF) {
            irfSum += val;
            if (val > irfMax) irfMax = val;
            if (val < irfMin) irfMin = val;
        }

        qDebug() << "导入IRF - sum:" << irfSum << ", max:" << irfMax << ", min:" << irfMin;
        qDebug() << "导入IRF - 数据类型: double (从文件读取), 数据点数:" << importedIRF.size();

        // 与模拟IRF进行比较
        if (gauss[0] != 0) {  // 如果模拟IRF已生成
            double gaussSum = 0.0, gaussMax = 0.0;
            for (int i = 0; i < 4096; ++i) {
                gaussSum += gauss[i];
                if (gauss[i] > gaussMax) gaussMax = gauss[i];
            }

            qDebug() << "=== IRF数据对比 ===";
            qDebug() << "模拟IRF - sum:" << gaussSum << ", max:" << gaussMax;
            qDebug() << "导入IRF - sum:" << irfSum << ", max:" << irfMax;
            qDebug() << "sum比值 (导入/模拟):" << (gaussSum > 0 ? irfSum/gaussSum : 0);
            qDebug() << "max比值 (导入/模拟):" << (gaussMax > 0 ? irfMax/gaussMax : 0);
            qDebug() << "数据点数比较 - 模拟:" << 4096 << ", 导入:" << importedIRF.size();
        }
    } else {
        qDebug() << "导入IRF数据为空";
    }
}
