/*
Copyright 2014 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
*/
#ifndef BOOST_ALIGN_DETAIL_ALIGNMENT_OF_CXX11_HPP
#define BOOST_ALIGN_DETAIL_ALIGNMENT_OF_CXX11_HPP

#include <type_traits>

namespace boost {
namespace alignment {
namespace detail {

using std::alignment_of;

} /* detail */
} /* alignment */
} /* boost */

#endif
