#include "FitManager.h"
#include <QDebug>
#include <QRandomGenerator>
#include "../Constants.h"
#include "../ThemeManager.h"

// 初始化静态实例
FitManager* FitManager::instance = nullptr;

FitManager* FitManager::getInstance() {
    if (!instance) {
        instance = new FitManager();
    }
    return instance;
}

FitManager::FitManager(QObject* parent) : QObject(parent) {
    registerModels();

    // 连接FitParameterModel的信号，当参数数量变化时更新模型
    connect(FitParameterModel::getInstance(), &FitParameterModel::modelParametersCountChanged,
            this, &FitManager::updateNExponentialModel);
}

FitManager::~FitManager() {
    // 清理资源
    m_models.clear();
}

void FitManager::registerModels() {
    // 注册所有可用的拟合模型（同时支持中文和英文名称）

    // N指数衰减模型
    QSharedPointer<FitModelInterface> nExponentialModel(new NExponentialDecayModel(FitParameterModel::getInstance()->modelParametersCount()));
    m_models["N指数衰减"] = nExponentialModel;

    // 高斯分布模型
    QSharedPointer<FitModelInterface> gaussianModel(new GaussianModel());
    m_models["高斯分布"] = gaussianModel;
    m_models["Gaussian"] = gaussianModel;  // 添加英文名称

    // 洛伦兹分布模型
    QSharedPointer<FitModelInterface> lorentzianModel(new LorentzianModel());
    m_models["洛伦兹分布"] = lorentzianModel;
    m_models["Lorentzian"] = lorentzianModel;  // 添加英文名称

    // 添加拉伸指数模型的映射（如果需要）
    if (m_models.contains("单指数衰减")) {
        m_models["Stretched Exponential"] = m_models["单指数衰减"];  // 临时映射到单指数模型
    }
}

QStringList FitManager::getAvailableModels() const {
    return m_models.keys();
}

QSharedPointer<FitModelInterface> FitManager::getModel(const QString& modelName) {
    if (m_models.contains(modelName)) {
        return m_models[modelName];
    }
    return nullptr;
}

FitResult FitManager::performFit(
    const QString& modelName,
    const QVector<double>& x,
    const QVector<double>& y,
    double xMin,
    double xMax
    ) {
    FitResult result;

    // 获取指定的拟合模型
    QSharedPointer<FitModelInterface> model = getModel(modelName);
    if (!model) {
        result.errorMessage = QString("找不到拟合模型: %1").arg(modelName);
        return result;
    }

    // 如果是N指数衰减模型，根据FitParameterModel中的modelAlgorithm设置拟合算法
    if (modelName == "N指数衰减") {
        NExponentialDecayModel* nExpModel = dynamic_cast<NExponentialDecayModel*>(model.data());
        if (nExpModel) {
            QString algorithm = FitParameterModel::getInstance()->modelAlgorithm();
            if (algorithm == "Least Square Method") {
                nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
                qDebug() << "Setting algorithm to Least Square Method";
            } else if (algorithm == "Maximum Likelihood Estimation") {
                nExpModel->setFitAlgorithm(NExponentialDecayModel::MaximumLikelihood);
                qDebug() << "Setting algorithm to Maximum Likelihood Estimation";
            } else if (algorithm == "Bayesian Analysis") {
                nExpModel->setFitAlgorithm(NExponentialDecayModel::BayesianAnalysis);
                qDebug() << "Setting algorithm to Bayesian Analysis";
            } else {
                // 默认使用最小二乘法
                nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
                qDebug() << "Unknown algorithm:" << algorithm << ", using default Least Square Method";
            }
        }
    }

    // 执行拟合
    result = model->fit(x, y, xMin, xMax);

    return result;
}

// 执行卷积拟合，
FitResult FitManager::performConvolutionFit(
    const QString& modelName,
    const QVector<double>& x,
    const QVector<double>& y,
    double xMin,
    double xMax
    ) {
    FitResult result;

    // 获取指定的拟合模型
    QSharedPointer<FitModelInterface> model = getModel(modelName);
    if (!model) {
        result.errorMessage = QString("找不到拟合模型: %1").arg(modelName);
        return result;
    }

    // 检查是否为N指数衰减模型
    NExponentialDecayModel* nExpModel = dynamic_cast<NExponentialDecayModel*>(model.data());
    if (!nExpModel) {
        result.errorMessage = "卷积拟合仅支持N指数衰减模型";
        return result;
    }

    // 根据FitParameterModel中的modelAlgorithm设置拟合算法
    QString algorithm = FitParameterModel::getInstance()->modelAlgorithm();
    if (algorithm == "Least Square Method") {
        nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
        qDebug() << "Setting algorithm to Least Square Method for convolution fitting";
    } else if (algorithm == "Maximum Likelihood Estimation") {
        nExpModel->setFitAlgorithm(NExponentialDecayModel::MaximumLikelihood);
        qDebug() << "Setting algorithm to Maximum Likelihood Estimation for convolution fitting";
    } else if (algorithm == "Bayesian Analysis") {
        nExpModel->setFitAlgorithm(NExponentialDecayModel::BayesianAnalysis);
        qDebug() << "Setting algorithm to Bayesian Analysis for convolution fitting";
    } else {
        // 默认使用最小二乘法
        nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
        qDebug() << "Unknown algorithm:" << algorithm << ", using default Least Square Method for convolution fitting";
    }

    // 执行卷积拟合
    result = nExpModel->fitConvolution(x, y, xMin, xMax);

    return result;
}

FitResult FitManager::performFitAndStore(
    const QString& modelName,
    TabType tab,
    PlotDataType plotType,
    const QVector<double>& x,
    const QVector<double>& y,
    double xMin,
    double xMax
    ) {
    // 执行拟合
    FitResult result = performFit(modelName, x, y, xMin, xMax);

    // 如果拟合成功，将结果添加到SharedDataManager
    if (result.success) {
        SharedDataManager::getInstance()->addFitCurveFromResult(
            tab, plotType, result, modelName);

        // 发出拟合完成信号
        emit fitCompleted(plotType, result);
    }

    return result;
}

void FitManager::updateNExponentialModel(int count) {
    // 检查模型是否存在
    if (!m_models.contains("N指数衰减")) {
        return;
    }

    // 获取当前模型
    QSharedPointer<FitModelInterface> currentModel = m_models["N指数衰减"];

    // 检查是否为NExponentialDecayModel类型
    NExponentialDecayModel* nExpModel = dynamic_cast<NExponentialDecayModel*>(currentModel.data());
    if (nExpModel) {
        // 更新模型的n值
        nExpModel->setN(count);
        qDebug() << "Updated N-Exponential model with n =" << count;
    } else {
        // 如果不是正确的类型，则创建新的模型
        QSharedPointer<FitModelInterface> newModel(new NExponentialDecayModel(count));
        m_models["N指数衰减"] = newModel;
        qDebug() << "Created new N-Exponential model with n =" << count;
    }
}
