#include "GlobalFitting.h"
#include "FitParameterModel.h"
#include "FitModels.h"  // 添加这个头文件以使用NExponentialDecayModel
#include <Eigen/Dense>
#include <unsupported/Eigen/LevenbergMarquardt>
#include <boost/random.hpp>
#include <boost/math/distributions/normal.hpp>
#include <chrono>
#include <algorithm>
#include <cmath>
#include <random>
#include <QtCore/qglobal.h>
#include <QDebug>
#include <QMap>

GlobalFitting::GlobalFitting() {
}

GlobalFitting::~GlobalFitting() {
}

void GlobalFitting::setExponentialCount(int n) {
    m_n = n;
}

void GlobalFitting::setAlgorithm(Algorithm algorithm) {
    m_algorithm = algorithm;
}

void GlobalFitting::setIterations(int iterations) {
    m_iterations = iterations;
}

GlobalFitResult GlobalFitting::performGlobalFit(const GlobalFitData& data) {
    GlobalFitResult result;

    // 检查输入数据
    if (data.xData.isEmpty() || data.yData.isEmpty()) {
        result.errorMessage = "没有可用的数据进行全局拟合";
        return result;
    }

    if (data.xData.size() != data.yData.size()) {
        result.errorMessage = "x数据和y数据的曲线数量不匹配";
        return result;
    }

    int numCurves = data.xData.size();
    if (numCurves < 2) {
        result.errorMessage = "全局拟合至少需要2条曲线";
        return result;
    }

    qDebug() << "开始全局拟合，曲线数量:" << numCurves << "，指数项数量:" << m_n;

    try {
        // 根据算法选择执行不同的拟合方法
        switch (m_algorithm) {
        case LeastSquares:
            result = fitGlobalLeastSquares(data);
            break;
        case MaximumLikelihood:
        {
            // 先使用最小二乘法获取初始参数
            qDebug() << "全局拟合 - 先使用最小二乘法获取初始参数，再进行最大似然估计";
            GlobalFitResult lsResult = fitGlobalLeastSquares(data);
            if (lsResult.success) {
                qDebug() << "最小二乘法初始参数获取完成，开始全局最大似然估计";
                result = fitGlobalMaximumLikelihood(data);
                // 使用最小二乘法的结果作为初始参数（这里需要在实现中处理）
            } else {
                result = lsResult;
            }
            break;
        }
        case BayesianAnalysis:
        {
            // 先使用最小二乘法获取初始参数
            qDebug() << "全局拟合 - 先使用最小二乘法获取初始参数，再进行贝叶斯分析";
            GlobalFitResult lsResult = fitGlobalLeastSquares(data);
            if (lsResult.success) {
                qDebug() << "最小二乘法初始参数获取完成，开始全局贝叶斯分析";
                result = fitGlobalBayesian(data);
                // 使用最小二乘法的结果作为初始参数（这里需要在实现中处理）
            } else {
                result = lsResult;
            }
            break;
        }
        default:
            result = fitGlobalLeastSquares(data);
            break;
        }

        if (result.success) {
            qDebug() << "全局拟合成功完成";
        } else {
            qDebug() << "全局拟合失败:" << result.errorMessage;
        }

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("全局拟合过程中发生异常: %1").arg(e.what());
        qDebug() << "全局拟合异常:" << e.what();
    }

    return result;
}

Eigen::VectorXd GlobalFitting::getInitialParameters(const GlobalFitData& data) {
    int numCurves = data.xData.size();

    // 参数结构：
    // [τ₁, τ₂, ..., τₙ, A₁₁, A₁₂, ..., A₁ₙ, B₁, A₂₁, A₂₂, ..., A₂ₙ, B₂, ...]
    // 共享参数：n个τ
    // 每条曲线特异性参数：n个A + 1个B
    int totalParams = m_n + numCurves * (m_n + 1);
    Eigen::VectorXd params(totalParams);

    qDebug() << "开始预拟合阶段：对每条曲线单独进行最小二乘法拟合以获取更好的初始参数";

    // 存储每条曲线的拟合结果
    QVector<Eigen::VectorXd> curveParams(numCurves);
    QVector<double> tauSums(m_n, 0.0); // 用于计算τ的平均值

    // 对每条曲线单独进行最小二乘法拟合
    for (int curve = 0; curve < numCurves; ++curve) {
        qDebug() << "预拟合曲线" << curve;

        // 获取当前曲线的数据
        const QVector<double>& x = data.xData[curve];
        const QVector<double>& y = data.yData[curve];

        // 获取拟合范围
        int curveSize = y.size();
        int xMin = static_cast<int>(data.xMin);
        int xMax = static_cast<int>(data.xMax);

        // 确保范围在有效范围内
        xMin = qBound(0, xMin, curveSize - 1);
        xMax = qBound(xMin, xMax, curveSize - 1);

        // 创建归一化的数据（与FitModels.cpp保持一致）
        QVector<double> xFiltered, yFiltered;
        for (int i = xMin; i <= xMax; ++i) {
            xFiltered.append(x[i] - x[xMin]); // 时间归一化
            yFiltered.append(y[i]);
        }

        if (xFiltered.size() < 3 * (2 * m_n + 1)) {
            qDebug() << "曲线" << curve << "数据点不足，使用默认参数";
            // 使用默认参数
            curveParams[curve] = Eigen::VectorXd(2 * m_n + 1);
            for (int i = 0; i < m_n; ++i) {
                curveParams[curve](2*i) = 100.0;     // A_i
                curveParams[curve](2*i+1) = 1.0 + i * 2.0; // τ_i
            }
            curveParams[curve](2*m_n) = 0.0; // B
            continue;
        }

        // 创建临时的N指数衰减模型进行拟合
        NExponentialDecayModel tempModel(m_n);
        tempModel.setFitAlgorithm(NExponentialDecayModel::LeastSquares); // 使用最小二乘法

        // 设置初始参数（从FitParameterModel获取或使用默认值）
        FitParameterModel* paramModel = FitParameterModel::getInstance();
        QVector<FitParameterModel::ParameterInfo> modelParams = paramModel->getParameters();
        QMap<QString, double> paramValues;
        for (const auto& param : modelParams) {
            paramValues[param.name] = param.value;
        }

        // 设置模型参数
        for (int i = 1; i <= m_n; ++i) {
            QString ampName = QString("A%1").arg(i);
            QString tauName = QString("τ%1").arg(i);

            if (paramValues.contains(ampName)) {
                tempModel.setParameter(ampName, paramValues[ampName]);
            } else {
                tempModel.setParameter(ampName, 100.0);
            }

            if (paramValues.contains(tauName)) {
                tempModel.setParameter(tauName, paramValues[tauName]);
            } else {
                tempModel.setParameter(tauName, 1.0 + i * 2.0);
            }
        }

        if (paramValues.contains("B")) {
            tempModel.setParameter("B", paramValues["B"]);
        } else {
            // 估计背景值
            int bgPoints = std::min(10, (xMax - xMin + 1) / 10);
            bgPoints = std::max(1, bgPoints);
            double estimatedB = 0.0;
            for (int i = xMax - bgPoints + 1; i <= xMax; ++i) {
                if (i >= 0 && i < y.size()) {
                    estimatedB += y[i];
                }
            }
            estimatedB /= bgPoints;
            tempModel.setParameter("B", estimatedB);
        }

        // 执行最小二乘法拟合
        try {
            FitResult fitResult = tempModel.fit(xFiltered, yFiltered, 0, xFiltered.size() - 1);

            if (fitResult.success) {
                // 将拟合结果转换为Eigen向量格式
                curveParams[curve] = Eigen::VectorXd(2 * m_n + 1);
                for (int i = 0; i < m_n; ++i) {
                    QString ampName = QString("A%1").arg(i + 1);
                    QString tauName = QString("τ%1").arg(i + 1);
                    curveParams[curve](2*i) = fitResult.parameters[ampName];     // A_i
                    curveParams[curve](2*i+1) = fitResult.parameters[tauName];  // τ_i
                    tauSums[i] += fitResult.parameters[tauName]; // 累加τ值用于计算平均值
                }
                curveParams[curve](2*m_n) = fitResult.parameters["B"]; // B

                qDebug() << "曲线" << curve << "预拟合结果:";
                for (int i = 0; i < m_n; ++i) {
                    qDebug() << "  A" << i+1 << "=" << curveParams[curve](2*i) << ", τ" << i+1 << "=" << curveParams[curve](2*i+1);
                }
                qDebug() << "  B=" << curveParams[curve](2*m_n);
            } else {
                qDebug() << "曲线" << curve << "预拟合失败:" << fitResult.errorMessage << "，使用默认参数";
                // 使用默认参数
                curveParams[curve] = Eigen::VectorXd(2 * m_n + 1);
                for (int i = 0; i < m_n; ++i) {
                    curveParams[curve](2*i) = 100.0;     // A_i
                    curveParams[curve](2*i+1) = 1.0 + i * 2.0; // τ_i
                    tauSums[i] += curveParams[curve](2*i+1);
                }
                curveParams[curve](2*m_n) = 0.0; // B
            }

        } catch (const std::exception& e) {
            qDebug() << "曲线" << curve << "预拟合异常:" << e.what() << "，使用默认参数";
            // 使用默认参数
            curveParams[curve] = Eigen::VectorXd(2 * m_n + 1);
            for (int i = 0; i < m_n; ++i) {
                curveParams[curve](2*i) = 100.0;     // A_i
                curveParams[curve](2*i+1) = 1.0 + i * 2.0; // τ_i
                tauSums[i] += curveParams[curve](2*i+1);
            }
            curveParams[curve](2*m_n) = 0.0; // B
        }
    }

    // 计算共享的τ参数（使用所有曲线的平均值）
    qDebug() << "计算共享τ参数:";
    for (int i = 0; i < m_n; ++i) {
        params(i) = tauSums[i] / numCurves;
        qDebug() << "共享τ" << i+1 << "=" << params(i);
    }

    // 设置每条曲线的A和B参数（使用预拟合的结果）
    qDebug() << "设置每条曲线的A和B参数:";
    for (int curve = 0; curve < numCurves; ++curve) {
        int baseIndex = m_n + curve * (m_n + 1);

        qDebug() << "曲线" << curve << "参数设置:";

        // 使用预拟合结果设置A参数
        for (int i = 0; i < m_n; ++i) {
            params(baseIndex + i) = curveParams[curve](2*i); // A_i
            qDebug() << "  A" << i+1 << "=" << params(baseIndex + i);
        }

        // 使用预拟合结果设置B参数
        params(baseIndex + m_n) = curveParams[curve](2*m_n); // B
        qDebug() << "  B=" << params(baseIndex + m_n);
    }

    qDebug() << "预拟合阶段完成，获得了更好的初始参数";
    return params;
}

void GlobalFitting::extractParameters(const Eigen::VectorXd& params,
                                     QMap<QString, double>& sharedParams,
                                     QVector<QMap<QString, double>>& groupParams,
                                     int numCurves) {
    sharedParams.clear();
    groupParams.clear();
    groupParams.resize(numCurves);

    // 提取共享的τ参数
    for (int i = 0; i < m_n; ++i) {
        QString tauName = QString("τ%1").arg(i + 1);
        sharedParams[tauName] = params(i);
    }

    // 提取每条曲线的A和B参数
    for (int curve = 0; curve < numCurves; ++curve) {
        int baseIndex = m_n + curve * (m_n + 1);
        QMap<QString, double> curveParams;

        // A参数
        for (int i = 0; i < m_n; ++i) {
            QString ampName = QString("A%1").arg(i + 1);
            curveParams[ampName] = params(baseIndex + i);
        }

        // B参数
        curveParams["B"] = params(baseIndex + m_n);

        groupParams[curve] = curveParams;
    }
}

QVector<double> GlobalFitting::calculateGlobalModel(const QVector<double>& x,
                                                   const QMap<QString, double>& sharedParams,
                                                   const QMap<QString, double>& groupParams) {
    QVector<double> result;

    // 获取时间归一化的参考点（与FitModels.cpp保持一致）
    double x0 = x.isEmpty() ? 0.0 : x.first();

    for (double xi : x) {
        double yi = groupParams["B"]; // 基线

        // 计算所有指数项的和（使用归一化时间，与FitModels.cpp保持完全一致）
        for (int i = 1; i <= m_n; ++i) {
            QString tauName = QString("τ%1").arg(i);
            QString ampName = QString("A%1").arg(i);

            if (sharedParams.contains(tauName) && groupParams.contains(ampName)) {
                double tau = sharedParams[tauName];
                double A = groupParams[ampName];
                // 关键修复：使用与FitModels.cpp完全一致的时间归一化方式
                double relativeTime = xi - x0;  // 计算相对时间
                yi += A * exp(-relativeTime / tau);
            }
        }

        result.append(yi);
    }

    return result;
}

GlobalFitResult GlobalFitting::fitGlobalLeastSquares(const GlobalFitData& data) {
    GlobalFitResult result;

    // 全局最小二乘法拟合函数对象
    struct GlobalLSFunctor : Eigen::DenseFunctor<double> {
        int m_n;
        int m_numCurves;
        const GlobalFitData& m_data;

        GlobalLSFunctor(int n, int numCurves, const GlobalFitData& data)
            : Eigen::DenseFunctor<double>(n + numCurves * (n + 1), 0), // inputs, values (values计算在构造后)
              m_n(n), m_numCurves(numCurves), m_data(data) {
            // 计算总数据点数，考虑拟合范围
            int totalDataPoints = 0;
            for (int curve = 0; curve < m_numCurves; ++curve) {
                int curveSize = m_data.yData[curve].size();
                int xMin = static_cast<int>(m_data.xMin);
                int xMax = static_cast<int>(m_data.xMax);

                // 确保范围在有效范围内
                xMin = qBound(0, xMin, curveSize - 1);
                xMax = qBound(xMin, xMax, curveSize - 1);

                totalDataPoints += (xMax - xMin + 1);
            }
            // 更新values数量
            const_cast<int&>(this->m_values) = totalDataPoints;
        }

        int operator()(const Eigen::VectorXd& params, Eigen::VectorXd& fvec) const {
            fvec.resize(values());
            int fvecIndex = 0;

            // 对每条曲线计算残差，考虑拟合范围
            for (int curve = 0; curve < m_numCurves; ++curve) {
                const QVector<double>& x = m_data.xData[curve];
                const QVector<double>& y = m_data.yData[curve];

                // 获取拟合范围
                int curveSize = y.size();
                int xMin = static_cast<int>(m_data.xMin);
                int xMax = static_cast<int>(m_data.xMax);

                // 确保范围在有效范围内
                xMin = qBound(0, xMin, curveSize - 1);
                xMax = qBound(xMin, xMax, curveSize - 1);

                // 获取当前曲线的参数
                int baseIndex = m_n + curve * (m_n + 1);

                // 获取时间归一化的参考点（与FitModels.cpp保持一致）
                double x0 = x[xMin]; // 使用拟合范围的起始点作为参考

                // 只在拟合范围内计算残差
                for (int i = xMin; i <= xMax; ++i) {
                    double modelValue = params(baseIndex + m_n); // B值

                    // 计算指数衰减项（使用归一化时间，与FitModels.cpp保持完全一致）
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);           // 共享的τ
                        double A = params(baseIndex + j); // 当前曲线的A
                        // 关键修复：使用与FitModels.cpp完全一致的时间归一化方式
                        double relativeTime = x[i] - x0;  // 计算相对时间
                        modelValue += A * exp(-relativeTime / tau);
                    }

                    // 计算残差
                    fvec(fvecIndex++) = y[i] - modelValue;
                }
            }

            return 0;
        }

        // 计算雅可比矩阵
        int df(const Eigen::VectorXd& params, Eigen::MatrixXd& fjac) const {
            int totalDataPoints = values();
            int totalParams = inputs();

            fjac.resize(totalDataPoints, totalParams);
            fjac.setZero();

            int fvecIndex = 0;

            // 对每条曲线计算雅可比矩阵，考虑拟合范围
            for (int curve = 0; curve < m_numCurves; ++curve) {
                const QVector<double>& x = m_data.xData[curve];

                // 获取拟合范围
                int curveSize = x.size();
                int xMin = static_cast<int>(m_data.xMin);
                int xMax = static_cast<int>(m_data.xMax);

                // 确保范围在有效范围内
                xMin = qBound(0, xMin, curveSize - 1);
                xMax = qBound(xMin, xMax, curveSize - 1);

                int baseIndex = m_n + curve * (m_n + 1);

                // 获取时间归一化的参考点（与FitModels.cpp保持一致）
                double x0 = x[xMin]; // 使用拟合范围的起始点作为参考

                // 只在拟合范围内计算雅可比矩阵
                for (int i = xMin; i <= xMax; ++i) {
                    // 关键修复：使用与FitModels.cpp完全一致的时间归一化方式
                    double relativeTime = x[i] - x0;  // 计算相对时间

                    // 对共享的τ参数求偏导
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);
                        double A = params(baseIndex + j);
                        double exp_term = exp(-relativeTime / tau);

                        // ∂f/∂τ = A * relativeTime * exp(-relativeTime/τ) / τ²
                        fjac(fvecIndex, j) = -A * relativeTime * exp_term / (tau * tau);
                    }

                    // 对当前曲线的A参数求偏导
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);
                        double exp_term = exp(-relativeTime / tau);

                        // ∂f/∂A = -exp(-relativeTime/τ)
                        fjac(fvecIndex, baseIndex + j) = -exp_term;
                    }

                    // 对当前曲线的B参数求偏导
                    // ∂f/∂B = -1
                    fjac(fvecIndex, baseIndex + m_n) = -1.0;

                    fvecIndex++;
                }
            }

            return 0;
        }
    };

    try {
        // 获取初始参数
        Eigen::VectorXd params = getInitialParameters(data);

        // 创建函数对象
        GlobalLSFunctor functor(m_n, data.xData.size(), data);

        // 使用Levenberg-Marquardt算法进行优化
        Eigen::LevenbergMarquardt<GlobalLSFunctor> lm(functor);
        lm.setMaxfev(m_iterations);

        // 设置收敛容差
        double tolerance = FitParameterModel::getInstance()->tolerance();
        lm.setXtol(tolerance);   // 参数收敛容差
        lm.setFtol(tolerance);   // 函数值收敛容差
        qDebug() << "全局最小二乘法 - 使用迭代次数:" << m_iterations << "，容差:" << tolerance;

        qDebug() << "开始全局最小二乘法优化...";
        Eigen::LevenbergMarquardtSpace::Status status = lm.minimize(params);

        if (status == Eigen::LevenbergMarquardtSpace::CosinusTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::RelativeReductionTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::RelativeErrorTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::RelativeErrorAndReductionTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::FtolTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::XtolTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::GtolTooSmall) {
            qDebug() << "全局最小二乘法优化成功";
            result.success = true;
        } else if (status == Eigen::LevenbergMarquardtSpace::TooManyFunctionEvaluation) {
            qDebug() << "全局最小二乘法优化达到最大迭代次数";
            result.success = true; // 仍然认为成功，只是可能精度不够
        } else {
            qDebug() << "全局最小二乘法优化失败，状态码:" << static_cast<int>(status);
            result.success = false;
            result.errorMessage = QString("优化失败，状态码: %1").arg(static_cast<int>(status));
        }

        // 提取参数
        extractParameters(params, result.sharedParameters, result.groupSpecificParameters, data.xData.size());

        // 输出优化后的参数用于调试
        qDebug() << "全局拟合优化后参数:";
        for (auto it = result.sharedParameters.begin(); it != result.sharedParameters.end(); ++it) {
            qDebug() << it.key() << "=" << it.value();
        }
        for (int curve = 0; curve < result.groupSpecificParameters.size(); ++curve) {
            qDebug() << "曲线" << curve << ":";
            for (auto it = result.groupSpecificParameters[curve].begin();
                 it != result.groupSpecificParameters[curve].end(); ++it) {
                qDebug() << "  " << it.key() << "=" << it.value();
            }
        }

        // 计算拟合曲线 - 只生成参与拟合范围内的曲线，与尾部拟合保持一致
        result.xData.resize(data.xData.size());
        result.yData.resize(data.xData.size());
        for (int curve = 0; curve < data.xData.size(); ++curve) {
            const QVector<double>& fullX = data.xData[curve];

            // 获取拟合范围
            int curveSize = fullX.size();
            int xMin = static_cast<int>(data.xMin);
            int xMax = static_cast<int>(data.xMax);

            // 确保范围在有效范围内
            xMin = qBound(0, xMin, curveSize - 1);
            xMax = qBound(xMin, xMax, curveSize - 1);

            // 只生成拟合范围内的数据点
            QVector<double> fitX, fitY;
            for (int i = xMin; i <= xMax; ++i) {
                fitX.append(fullX[i]);
            }

            // 计算对应的拟合曲线
            fitY = calculateGlobalModel(fitX, result.sharedParameters, result.groupSpecificParameters[curve]);

            result.xData[curve] = fitX;
            result.yData[curve] = fitY;
        }

        // 计算拟合质量指标
        result.globalChiSquare = calculateGlobalChiSquare(data, result.sharedParameters, result.groupSpecificParameters);
        result.globalRSquare = calculateGlobalRSquared(data, result.sharedParameters, result.groupSpecificParameters);
        result.fitMethod = 1.0; // Least Squares

        // 保存迭代次数和容差信息
        result.sharedParameters["Iterations"] = static_cast<double>(m_iterations);
        result.sharedParameters["Tolerance"] = FitParameterModel::getInstance()->tolerance();

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("全局最小二乘法拟合失败: %1").arg(e.what());
    }

    return result;
}

GlobalFitResult GlobalFitting::fitGlobalMaximumLikelihood(const GlobalFitData& data) {
    GlobalFitResult result;

    // 全局最大似然估计拟合函数对象
    struct GlobalMLEFunctor : Eigen::DenseFunctor<double> {
        int m_n;
        int m_numCurves;
        const GlobalFitData& m_data;

        GlobalMLEFunctor(int n, int numCurves, const GlobalFitData& data)
            : Eigen::DenseFunctor<double>(n + numCurves * (n + 1), 0), // inputs, values (values计算在构造后)
              m_n(n), m_numCurves(numCurves), m_data(data) {
            // 计算总数据点数，考虑拟合范围
            int totalDataPoints = 0;
            for (int curve = 0; curve < m_numCurves; ++curve) {
                int curveSize = m_data.yData[curve].size();
                int xMin = static_cast<int>(m_data.xMin);
                int xMax = static_cast<int>(m_data.xMax);

                // 确保范围在有效范围内
                xMin = qBound(0, xMin, curveSize - 1);
                xMax = qBound(xMin, xMax, curveSize - 1);

                totalDataPoints += (xMax - xMin + 1);
            }
            // 更新values数量
            const_cast<int&>(this->m_values) = totalDataPoints;
        }

        int operator()(const Eigen::VectorXd& params, Eigen::VectorXd& fvec) const {
            fvec.resize(values());
            int fvecIndex = 0;

            // 对每条曲线计算MLE残差，考虑拟合范围
            for (int curve = 0; curve < m_numCurves; ++curve) {
                const QVector<double>& x = m_data.xData[curve];
                const QVector<double>& y = m_data.yData[curve];

                // 获取拟合范围
                int curveSize = y.size();
                int xMin = static_cast<int>(m_data.xMin);
                int xMax = static_cast<int>(m_data.xMax);

                // 确保范围在有效范围内
                xMin = qBound(0, xMin, curveSize - 1);
                xMax = qBound(xMin, xMax, curveSize - 1);

                // 获取当前曲线的参数
                int baseIndex = m_n + curve * (m_n + 1);

                // 获取时间归一化的参考点
                double x0 = x[xMin]; // 使用拟合范围的起始点作为参考

                // 只在拟合范围内计算MLE残差
                for (int i = xMin; i <= xMax; ++i) {
                    double modelValue = params(baseIndex + m_n); // B值

                    // 计算指数衰减项（使用归一化时间）
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);           // 共享的τ
                        double A = params(baseIndex + j); // 当前曲线的A
                        double relativeTime = x[i] - x0;  // 计算相对时间
                        modelValue += A * exp(-relativeTime / tau);
                    }

                    // 避免除以零或负值
                    if (modelValue <= 0) modelValue = 1e-6;

                    // 使用泊松分布的特性：方差等于均值
                    // 标准化残差 = (观测值 - 预测值) / sqrt(预测值)
                    fvec(fvecIndex++) = (y[i] - modelValue) / sqrt(modelValue);
                }
            }

            return 0;
        }

        // 计算雅可比矩阵
        int df(const Eigen::VectorXd& params, Eigen::MatrixXd& fjac) const {
            int totalDataPoints = values();
            int totalParams = inputs();

            fjac.resize(totalDataPoints, totalParams);
            fjac.setZero();

            int fvecIndex = 0;

            // 对每条曲线计算雅可比矩阵，考虑拟合范围
            for (int curve = 0; curve < m_numCurves; ++curve) {
                const QVector<double>& x = m_data.xData[curve];
                const QVector<double>& y = m_data.yData[curve];

                // 获取拟合范围
                int curveSize = y.size();
                int xMin = static_cast<int>(m_data.xMin);
                int xMax = static_cast<int>(m_data.xMax);

                // 确保范围在有效范围内
                xMin = qBound(0, xMin, curveSize - 1);
                xMax = qBound(xMin, xMax, curveSize - 1);

                int baseIndex = m_n + curve * (m_n + 1);

                // 获取时间归一化的参考点
                double x0 = x[xMin]; // 使用拟合范围的起始点作为参考

                // 只在拟合范围内计算雅可比矩阵
                for (int i = xMin; i <= xMax; ++i) {
                    double relativeTime = x[i] - x0;  // 计算相对时间

                    // 计算当前的模型值
                    double modelValue = params(baseIndex + m_n); // B值
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);
                        double A = params(baseIndex + j);
                        modelValue += A * exp(-relativeTime / tau);
                    }

                    // 避免除以零或负值
                    if (modelValue <= 0) modelValue = 1e-6;

                    double sqrtModelValue = sqrt(modelValue);

                    // 对共享的τ参数求偏导
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);
                        double A = params(baseIndex + j);
                        double exp_term = exp(-relativeTime / tau);

                        // ∂(residual)/∂τ = ∂((y - f) / sqrt(f))/∂τ
                        // = -1/sqrt(f) * ∂f/∂τ + (y - f)/(2*f^(3/2)) * ∂f/∂τ
                        double df_dtau = A * relativeTime * exp_term / (tau * tau);
                        fjac(fvecIndex, j) = -df_dtau / sqrtModelValue +
                                           (y[i] - modelValue) * df_dtau / (2 * modelValue * sqrtModelValue);
                    }

                    // 对当前曲线的A参数求偏导
                    for (int j = 0; j < m_n; ++j) {
                        double tau = params(j);
                        double exp_term = exp(-relativeTime / tau);

                        // ∂(residual)/∂A = ∂((y - f) / sqrt(f))/∂A
                        double df_dA = exp_term;
                        fjac(fvecIndex, baseIndex + j) = -df_dA / sqrtModelValue +
                                                       (y[i] - modelValue) * df_dA / (2 * modelValue * sqrtModelValue);
                    }

                    // 对当前曲线的B参数求偏导
                    // ∂(residual)/∂B = ∂((y - f) / sqrt(f))/∂B
                    double df_dB = 1.0;
                    fjac(fvecIndex, baseIndex + m_n) = -df_dB / sqrtModelValue +
                                                     (y[i] - modelValue) * df_dB / (2 * modelValue * sqrtModelValue);

                    fvecIndex++;
                }
            }

            return 0;
        }
    };

    try {
        // 先使用最小二乘法获取初始参数
        qDebug() << "全局拟合 - 先使用最小二乘法获取初始参数，再进行最大似然估计";
        GlobalFitResult lsResult = fitGlobalLeastSquares(data);
        if (!lsResult.success) {
            result.success = false;
            result.errorMessage = "无法获取最小二乘法初始参数";
            result.fitMethod = 2.0;
            return result;
        }

        // 将最小二乘法结果转换为初始参数
        Eigen::VectorXd params(m_n + data.xData.size() * (m_n + 1));

        // 设置共享的τ参数
        for (int i = 0; i < m_n; ++i) {
            QString tauName = QString("τ%1").arg(i + 1);
            params(i) = lsResult.sharedParameters[tauName];
        }

        // 设置每条曲线的A和B参数
        for (int curve = 0; curve < data.xData.size(); ++curve) {
            int baseIndex = m_n + curve * (m_n + 1);
            for (int i = 0; i < m_n; ++i) {
                QString ampName = QString("A%1").arg(i + 1);
                params(baseIndex + i) = lsResult.groupSpecificParameters[curve][ampName];
            }
            params(baseIndex + m_n) = lsResult.groupSpecificParameters[curve]["B"];
        }

        qDebug() << "最小二乘法初始参数获取完成，开始全局最大似然估计";

        // 创建函数对象
        GlobalMLEFunctor functor(m_n, data.xData.size(), data);

        // 使用Levenberg-Marquardt算法进行优化
        Eigen::LevenbergMarquardt<GlobalMLEFunctor> lm(functor);
        lm.setMaxfev(m_iterations);

        // 设置收敛容差
        double tolerance = FitParameterModel::getInstance()->tolerance();
        lm.setXtol(tolerance);   // 参数收敛容差
        lm.setFtol(tolerance);   // 函数值收敛容差
        qDebug() << "全局最大似然估计 - 使用迭代次数:" << m_iterations << "，容差:" << tolerance;

        qDebug() << "开始全局最大似然估计优化...";
        Eigen::LevenbergMarquardtSpace::Status status = lm.minimize(params);

        if (status == Eigen::LevenbergMarquardtSpace::CosinusTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::RelativeReductionTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::RelativeErrorTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::RelativeErrorAndReductionTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::FtolTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::XtolTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::GtolTooSmall) {
            qDebug() << "全局最大似然估计优化成功";
            result.success = true;
        } else if (status == Eigen::LevenbergMarquardtSpace::TooManyFunctionEvaluation) {
            qDebug() << "全局最大似然估计优化达到最大迭代次数";
            result.success = true; // 仍然认为成功，只是可能精度不够
        } else {
            qDebug() << "全局最大似然估计优化失败，状态码:" << static_cast<int>(status);
            result.success = false;
            result.errorMessage = QString("优化失败，状态码: %1").arg(static_cast<int>(status));
        }

        // 提取参数
        extractParameters(params, result.sharedParameters, result.groupSpecificParameters, data.xData.size());

        // 计算拟合曲线 - 只生成参与拟合范围内的曲线
        result.xData.resize(data.xData.size());
        result.yData.resize(data.xData.size());
        for (int curve = 0; curve < data.xData.size(); ++curve) {
            const QVector<double>& fullX = data.xData[curve];

            // 获取拟合范围
            int curveSize = fullX.size();
            int xMin = static_cast<int>(data.xMin);
            int xMax = static_cast<int>(data.xMax);

            // 确保范围在有效范围内
            xMin = qBound(0, xMin, curveSize - 1);
            xMax = qBound(xMin, xMax, curveSize - 1);

            // 只生成拟合范围内的数据点
            QVector<double> fitX, fitY;
            for (int i = xMin; i <= xMax; ++i) {
                fitX.append(fullX[i]);
            }

            // 计算对应的拟合曲线
            fitY = calculateGlobalModel(fitX, result.sharedParameters, result.groupSpecificParameters[curve]);

            result.xData[curve] = fitX;
            result.yData[curve] = fitY;
        }

        // 计算拟合质量指标
        result.globalChiSquare = calculateGlobalChiSquare(data, result.sharedParameters, result.groupSpecificParameters);
        result.globalRSquare = calculateGlobalRSquared(data, result.sharedParameters, result.groupSpecificParameters);
        result.fitMethod = 2.0; // Maximum Likelihood

        // 保存迭代次数和容差信息
        result.sharedParameters["Iterations"] = static_cast<double>(m_iterations);
        result.sharedParameters["Tolerance"] = FitParameterModel::getInstance()->tolerance();

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("全局最大似然估计拟合失败: %1").arg(e.what());
    }

    return result;
}

GlobalFitResult GlobalFitting::fitGlobalBayesian(const GlobalFitData& data) {
    GlobalFitResult result;

    try {
        // 先使用最小二乘法获取初始参数
        qDebug() << "全局拟合 - 先使用最小二乘法获取初始参数，再进行贝叶斯分析";
        GlobalFitResult lsResult = fitGlobalLeastSquares(data);
        if (!lsResult.success) {
            result.success = false;
            result.errorMessage = "无法获取最小二乘法初始参数";
            result.fitMethod = 3.0;
            return result;
        }

        qDebug() << "最小二乘法初始参数获取完成，开始全局贝叶斯分析";

        // 将最小二乘法结果转换为初始参数
        Eigen::VectorXd initialParams(m_n + data.xData.size() * (m_n + 1));

        // 设置共享的τ参数
        for (int i = 0; i < m_n; ++i) {
            QString tauName = QString("τ%1").arg(i + 1);
            initialParams(i) = lsResult.sharedParameters[tauName];
        }

        // 设置每条曲线的A和B参数
        for (int curve = 0; curve < data.xData.size(); ++curve) {
            int baseIndex = m_n + curve * (m_n + 1);
            for (int i = 0; i < m_n; ++i) {
                QString ampName = QString("A%1").arg(i + 1);
                initialParams(baseIndex + i) = lsResult.groupSpecificParameters[curve][ampName];
            }
            initialParams(baseIndex + m_n) = lsResult.groupSpecificParameters[curve]["B"];
        }

        // 设置先验分布参数
        Eigen::VectorXd priorMeans = initialParams;
        Eigen::VectorXd priorStdDevs(initialParams.size());

        // 为共享的τ参数设置先验
        for (int i = 0; i < m_n; ++i) {
            priorStdDevs(i) = std::max(0.5, std::abs(initialParams(i) * 0.3)); // τ的标准差
        }

        // 为每条曲线的A和B参数设置先验
        for (int curve = 0; curve < data.xData.size(); ++curve) {
            int baseIndex = m_n + curve * (m_n + 1);
            for (int i = 0; i < m_n; ++i) {
                priorStdDevs(baseIndex + i) = std::max(10.0, std::abs(initialParams(baseIndex + i) * 0.3)); // A的标准差
            }
            priorStdDevs(baseIndex + m_n) = std::max(1.0, std::abs(initialParams(baseIndex + m_n) * 0.2)); // B的标准差
        }

        // 执行贝叶斯分析（Metropolis-Hastings采样）
        int numSamples = m_iterations / 2; // 使用迭代次数的一半作为样本数
        int burnIn = m_iterations / 10;    // 使用迭代次数的十分之一作为预热期
        qDebug() << "全局贝叶斯分析 - 样本数:" << numSamples << "，预热期:" << burnIn;

        QVector<Eigen::VectorXd> samples = globalMetropolisHastings(
            data, initialParams, priorMeans, priorStdDevs, numSamples, burnIn);
        qDebug() << "全局贝叶斯分析完成，获得" << samples.size() << "个样本";

        if (samples.isEmpty()) {
            result.success = false;
            result.errorMessage = "贝叶斯采样失败";
            result.fitMethod = 3.0;
            return result;
        }

        // 计算参数的后验均值和标准差
        Eigen::VectorXd posteriorMeans = Eigen::VectorXd::Zero(initialParams.size());
        for (const auto& sample : samples) {
            posteriorMeans += sample;
        }
        posteriorMeans /= samples.size();

        Eigen::VectorXd posteriorStdDevs = Eigen::VectorXd::Zero(initialParams.size());
        for (const auto& sample : samples) {
            Eigen::VectorXd diff = sample - posteriorMeans;
            posteriorStdDevs += diff.cwiseProduct(diff);
        }
        posteriorStdDevs = (posteriorStdDevs / (samples.size() - 1)).cwiseSqrt();

        // 提取参数
        extractParameters(posteriorMeans, result.sharedParameters, result.groupSpecificParameters, data.xData.size());

        // 保存参数不确定度
        for (int i = 0; i < m_n; ++i) {
            QString tauName = QString("τ%1").arg(i + 1);
            QString tauStdName = QString("τ%1_std").arg(i + 1);
            result.sharedParameterStdDevs[tauStdName] = posteriorStdDevs(i);
        }

        for (int curve = 0; curve < data.xData.size(); ++curve) {
            int baseIndex = m_n + curve * (m_n + 1);
            QMap<QString, double> curveStdDevs;
            for (int i = 0; i < m_n; ++i) {
                QString ampStdName = QString("A%1_std").arg(i + 1);
                curveStdDevs[ampStdName] = posteriorStdDevs(baseIndex + i);
            }
            curveStdDevs["B_std"] = posteriorStdDevs(baseIndex + m_n);
            result.groupParameterStdDevs.append(curveStdDevs);
        }

        // 计算拟合曲线 - 只生成参与拟合范围内的曲线
        result.xData.resize(data.xData.size());
        result.yData.resize(data.xData.size());
        for (int curve = 0; curve < data.xData.size(); ++curve) {
            const QVector<double>& fullX = data.xData[curve];

            // 获取拟合范围
            int curveSize = fullX.size();
            int xMin = static_cast<int>(data.xMin);
            int xMax = static_cast<int>(data.xMax);

            // 确保范围在有效范围内
            xMin = qBound(0, xMin, curveSize - 1);
            xMax = qBound(xMin, xMax, curveSize - 1);

            // 只生成拟合范围内的数据点
            QVector<double> fitX, fitY;
            for (int i = xMin; i <= xMax; ++i) {
                fitX.append(fullX[i]);
            }

            // 计算对应的拟合曲线
            fitY = calculateGlobalModel(fitX, result.sharedParameters, result.groupSpecificParameters[curve]);

            result.xData[curve] = fitX;
            result.yData[curve] = fitY;
        }

        // 计算拟合质量指标
        result.globalChiSquare = calculateGlobalChiSquare(data, result.sharedParameters, result.groupSpecificParameters);
        result.globalRSquare = calculateGlobalRSquared(data, result.sharedParameters, result.groupSpecificParameters);
        result.fitMethod = 3.0; // Bayesian Analysis

        // 保存迭代次数和容差信息
        result.sharedParameters["Iterations"] = static_cast<double>(m_iterations);
        result.sharedParameters["Tolerance"] = FitParameterModel::getInstance()->tolerance();

        result.success = true;

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("全局贝叶斯分析拟合失败: %1").arg(e.what());
    }

    return result;
}

double GlobalFitting::calculateGlobalChiSquare(const GlobalFitData& data,
                                              const QMap<QString, double>& sharedParams,
                                              const QVector<QMap<QString, double>>& groupParams) {
    double chiSquare = 0.0;

    for (int curve = 0; curve < data.xData.size(); ++curve) {
        const QVector<double>& x = data.xData[curve];
        const QVector<double>& y = data.yData[curve];

        // 获取拟合范围
        int curveSize = y.size();
        int xMin = static_cast<int>(data.xMin);
        int xMax = static_cast<int>(data.xMax);

        // 确保范围在有效范围内
        xMin = qBound(0, xMin, curveSize - 1);
        xMax = qBound(xMin, xMax, curveSize - 1);

        // 创建拟合范围内的x数据用于模型计算
        QVector<double> xFit;
        for (int i = xMin; i <= xMax; ++i) {
            xFit.append(x[i]);
        }

        QVector<double> modelY = calculateGlobalModel(xFit, sharedParams, groupParams[curve]);

        // 只在拟合范围内计算卡方值
        for (int i = 0; i < modelY.size(); ++i) {
            int dataIndex = xMin + i;
            if (dataIndex < y.size()) {
                double residual = y[dataIndex] - modelY[i];
                chiSquare += residual * residual;
            }
        }
    }

    return chiSquare;
}

double GlobalFitting::calculateGlobalRSquared(const GlobalFitData& data,
                                             const QMap<QString, double>& sharedParams,
                                             const QVector<QMap<QString, double>>& groupParams) {
    double ssRes = 0.0; // 残差平方和
    double ssTot = 0.0; // 总平方和
    double yMeanSum = 0.0;
    int totalPoints = 0;

    // 计算拟合范围内所有数据点的均值
    for (int curve = 0; curve < data.yData.size(); ++curve) {
        const QVector<double>& y = data.yData[curve];

        // 获取拟合范围
        int curveSize = y.size();
        int xMin = static_cast<int>(data.xMin);
        int xMax = static_cast<int>(data.xMax);

        // 确保范围在有效范围内
        xMin = qBound(0, xMin, curveSize - 1);
        xMax = qBound(xMin, xMax, curveSize - 1);

        // 只计算拟合范围内的均值
        for (int i = xMin; i <= xMax; ++i) {
            yMeanSum += y[i];
            totalPoints++;
        }
    }
    double yMean = yMeanSum / totalPoints;

    // 计算残差平方和和总平方和
    for (int curve = 0; curve < data.xData.size(); ++curve) {
        const QVector<double>& x = data.xData[curve];
        const QVector<double>& y = data.yData[curve];

        // 获取拟合范围
        int curveSize = y.size();
        int xMin = static_cast<int>(data.xMin);
        int xMax = static_cast<int>(data.xMax);

        // 确保范围在有效范围内
        xMin = qBound(0, xMin, curveSize - 1);
        xMax = qBound(xMin, xMax, curveSize - 1);

        // 创建拟合范围内的x数据用于模型计算
        QVector<double> xFit;
        for (int i = xMin; i <= xMax; ++i) {
            xFit.append(x[i]);
        }

        QVector<double> modelY = calculateGlobalModel(xFit, sharedParams, groupParams[curve]);

        // 只在拟合范围内计算R²
        for (int i = 0; i < modelY.size(); ++i) {
            int dataIndex = xMin + i;
            if (dataIndex < y.size()) {
                double residual = y[dataIndex] - modelY[i];
                ssRes += residual * residual;

                double deviation = y[dataIndex] - yMean;
                ssTot += deviation * deviation;
            }
        }
    }

    // 计算R²
    if (ssTot == 0.0) return 1.0;
    return 1.0 - (ssRes / ssTot);
}

// 全局贝叶斯分析的Metropolis-Hastings采样
QVector<Eigen::VectorXd> GlobalFitting::globalMetropolisHastings(const GlobalFitData& data,
                                                                 const Eigen::VectorXd& initialParams,
                                                                 const Eigen::VectorXd& priorMeans,
                                                                 const Eigen::VectorXd& priorStdDevs,
                                                                 int numSamples, int burnIn) {
    QVector<Eigen::VectorXd> samples;

    // 初始化随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<double> normalDist(0.0, 1.0);
    std::uniform_real_distribution<double> uniformDist(0.0, 1.0);

    Eigen::VectorXd currentParams = initialParams;
    double currentLogPosterior = calculateGlobalLogLikelihood(data, currentParams) +
                                calculateGlobalLogPrior(currentParams, priorMeans, priorStdDevs);

    int acceptedCount = 0;

    // 设置提议分布的标准差（自适应）
    Eigen::VectorXd proposalStdDevs = priorStdDevs * 0.1;

    for (int i = 0; i < numSamples + burnIn; ++i) {
        // 生成新的参数提议
        Eigen::VectorXd proposedParams = currentParams;
        for (int j = 0; j < proposedParams.size(); ++j) {
            proposedParams(j) += normalDist(gen) * proposalStdDevs(j);

            // 确保参数在合理范围内
            if (j < m_n) { // τ参数
                proposedParams(j) = std::max(0.01, proposedParams(j));
            } else { // A和B参数
                int paramType = (j - m_n) % (m_n + 1);
                if (paramType < m_n) { // A参数
                    proposedParams(j) = std::max(0.0, proposedParams(j));
                }
                // B参数可以为负值，不需要限制
            }
        }

        // 计算提议参数的后验概率
        double proposedLogPosterior = calculateGlobalLogLikelihood(data, proposedParams) +
                                     calculateGlobalLogPrior(proposedParams, priorMeans, priorStdDevs);

        // Metropolis-Hastings接受准则
        double logAcceptanceRatio = proposedLogPosterior - currentLogPosterior;
        double acceptanceProbability = std::min(1.0, std::exp(logAcceptanceRatio));

        if (uniformDist(gen) < acceptanceProbability) {
            currentParams = proposedParams;
            currentLogPosterior = proposedLogPosterior;
            acceptedCount++;
        }

        // 预热期后开始收集样本
        if (i >= burnIn) {
            samples.append(currentParams);
        }

        // 自适应调整提议分布（每100次迭代调整一次）
        if (i > 0 && i % 100 == 0) {
            double acceptanceRate = static_cast<double>(acceptedCount) / (i + 1);
            if (acceptanceRate < 0.2) {
                proposalStdDevs *= 0.9; // 降低步长
            } else if (acceptanceRate > 0.5) {
                proposalStdDevs *= 1.1; // 增加步长
            }
        }
    }

    qDebug() << "全局贝叶斯采样完成，接受率:" << (static_cast<double>(acceptedCount) / (numSamples + burnIn));

    return samples;
}

// 计算全局对数似然函数
double GlobalFitting::calculateGlobalLogLikelihood(const GlobalFitData& data, const Eigen::VectorXd& params) {
    double logLikelihood = 0.0;

    // 对每条曲线计算对数似然，考虑拟合范围
    for (int curve = 0; curve < data.xData.size(); ++curve) {
        const QVector<double>& x = data.xData[curve];
        const QVector<double>& y = data.yData[curve];

        // 获取拟合范围
        int curveSize = y.size();
        int xMin = static_cast<int>(data.xMin);
        int xMax = static_cast<int>(data.xMax);

        // 确保范围在有效范围内
        xMin = qBound(0, xMin, curveSize - 1);
        xMax = qBound(xMin, xMax, curveSize - 1);

        // 获取当前曲线的参数
        int baseIndex = m_n + curve * (m_n + 1);

        // 获取时间归一化的参考点
        double x0 = x[xMin]; // 使用拟合范围的起始点作为参考

        // 只在拟合范围内计算对数似然
        for (int i = xMin; i <= xMax; ++i) {
            double modelValue = params(baseIndex + m_n); // B值

            // 计算指数衰减项（使用归一化时间）
            for (int j = 0; j < m_n; ++j) {
                double tau = params(j);           // 共享的τ
                double A = params(baseIndex + j); // 当前曲线的A
                double relativeTime = x[i] - x0;  // 计算相对时间
                modelValue += A * exp(-relativeTime / tau);
            }

            // 避免除以零或负值
            if (modelValue <= 0) modelValue = 1e-6;

            // 使用泊松分布的对数似然：log P(y|λ) = y*log(λ) - λ - log(y!)
            // 忽略常数项 log(y!)，因为它不影响优化
            logLikelihood += y[i] * log(modelValue) - modelValue;
        }
    }

    return logLikelihood;
}

// 计算全局对数先验概率
double GlobalFitting::calculateGlobalLogPrior(const Eigen::VectorXd& params,
                                              const Eigen::VectorXd& priorMeans,
                                              const Eigen::VectorXd& priorStdDevs) {
    double logPrior = 0.0;

    for (int i = 0; i < params.size(); ++i) {
        // 使用正态分布作为先验：log P(θ) = -0.5 * ((θ - μ) / σ)^2 - log(σ√(2π))
        // 忽略常数项 log(σ√(2π))，因为它不影响优化
        double diff = params(i) - priorMeans(i);
        double variance = priorStdDevs(i) * priorStdDevs(i);
        logPrior -= 0.5 * (diff * diff) / variance;
    }

    return logPrior;
}
