///////////////////////////////////////////////////////////////////////////////
// external.hpp
//
//  Copyright 2005 <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_ACCUMULATORS_FRAMEWORK_EXTERNAL_HPP_EAN_01_12_2005
#define BOOST_ACCUMULATORS_FRAMEWORK_EXTERNAL_HPP_EAN_01_12_2005

#include <boost/mpl/apply.hpp>
#include <boost/accumulators/framework/accumulators/external_accumulator.hpp>

//namespace boost { namespace accumulators
//{
//
/////////////////////////////////////////////////////////////////////////////////
//// external
////
//template<typename Type>
//struct external
//{
//};
//
//}} // namespace boost::accumulators

#endif
