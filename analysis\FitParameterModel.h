#ifndef FITPARAMETERMODEL_H
#define FITPARAMETERMODEL_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QVector>
#include <QVariant>
#include "FitModelInterface.h"

// 拟合参数模型类 - 使用Qt属性系统实现数据绑定
class FitParameterModel : public QObject {
    Q_OBJECT

    // 定义属性
    Q_PROPERTY(QString analysisMethod READ analysisMethod WRITE setAnalysisMethod NOTIFY analysisMethodChanged)
    Q_PROPERTY(QString fittingModel READ fittingModel WRITE setFittingModel NOTIFY fittingModelChanged)
    Q_PROPERTY(double t0 READ t0 WRITE setT0 NOTIFY t0Changed)
    Q_PROPERTY(double fwhm READ fwhm WRITE setFwhm NOTIFY fwhmChanged)
    Q_PROPERTY(QString analysisMode READ analysisMode WRITE setAnalysisMode NOTIFY analysisModeChanged)
    Q_PROPERTY(QString exponentialModel READ exponentialModel WRITE setExponentialModel NOTIFY exponentialModelChanged)
    Q_PROPERTY(int modelParametersCount READ modelParametersCount WRITE setModelParametersCount NOTIFY modelParametersCountChanged)
    Q_PROPERTY(QString modelAlgorithm READ modelAlgorithm WRITE setModelAlgorithm NOTIFY modelAlgorithmChanged)
    Q_PROPERTY(int iterations READ iterations WRITE setIterations NOTIFY iterationsChanged)
    Q_PROPERTY(double tolerance READ tolerance WRITE setTolerance NOTIFY toleranceChanged)
    Q_PROPERTY(double rangeFrom READ rangeFrom WRITE setRangeFrom NOTIFY rangeFromChanged)
    Q_PROPERTY(double rangeTo READ rangeTo WRITE setRangeTo NOTIFY rangeToChanged)
    Q_PROPERTY(bool hasIRFData READ hasIRFData NOTIFY irfDataChanged)

public:
    static FitParameterModel* getInstance();
    ~FitParameterModel();

    // 参数结构体
    struct ParameterInfo {
        QString name;        // 参数名称
        QString displayName; // 显示名称（包含单位）
        double value;        // 参数值
        bool fixed;          // 是否固定
        bool hasMinConstraint; // 是否有最小值约束
        double minValue;     // 最小值
        bool hasMaxConstraint; // 是否有最大值约束
        double maxValue;     // 最大值
    };

    // 属性访问方法
    QString analysisMethod() const { return m_analysisMethod; }
    QString fittingModel() const { return m_fittingModel; }
    double t0() const { return m_t0; }
    double fwhm() const { return m_fwhm; }
    QString analysisMode() const { return m_analysisMode; }
    QString exponentialModel() const { return m_exponentialModel; }
    int modelParametersCount() const { return m_modelParametersCount; }
    QString modelAlgorithm() const { return m_modelAlgorithm; }
    int iterations() const { return m_iterations; }
    double tolerance() const { return m_tolerance; }
    double rangeFrom() const { return m_rangeFrom; }
    double rangeTo() const { return m_rangeTo; }
    bool hasIRFData() const { return !m_irfData.isEmpty(); }

    // 参数访问方法
    QVector<ParameterInfo> getParameters() const;
    ParameterInfo getParameter(const QString& name) const;
    QStringList getParameterNames() const;

    // 设置参数值
    void setParameterValue(const QString& name, double value);
    void setParameterFixed(const QString& name, bool fixed);
    void setParameterRange(const QString& name, bool hasMin, double min, bool hasMax, double max);

    // 创建拟合模型
    QSharedPointer<FitModelInterface> createFitModel();

    // IRF数据相关方法
    void setIRFData(const QVector<double>& irfData);
    QVector<double> getIRFData() const;
    void clearIRFData();

public slots:
    // 属性设置方法
    void setAnalysisMethod(const QString& method);
    void setFittingModel(const QString& model);
    void setT0(double value);
    void setFwhm(double value);
    void setAnalysisMode(const QString& mode);
    void setExponentialModel(const QString& model);
    void setModelParametersCount(int count);
    void setModelAlgorithm(const QString& algorithm);
    void setIterations(int count);
    void setTolerance(double value);
    void setRangeFrom(double value);
    void setRangeTo(double value);

    // 重置所有参数到默认值
    void resetToDefaults();

signals:
    // 属性变化信号
    void analysisMethodChanged(const QString& method);
    void fittingModelChanged(const QString& model);
    void t0Changed(double value);
    void fwhmChanged(double value);
    void analysisModeChanged(const QString& mode);
    void exponentialModelChanged(const QString& model);
    void modelParametersCountChanged(int count);
    void modelAlgorithmChanged(const QString& algorithm);
    void iterationsChanged(int count);
    void toleranceChanged(double value);
    void rangeFromChanged(double value);
    void rangeToChanged(double value);
    void irfDataChanged();

    // 参数变化信号
    void parametersChanged();
    void parameterChanged(const QString& name);

private:
    FitParameterModel(QObject* parent = nullptr);
    static FitParameterModel* instance;

    // 更新参数列表
    void updateParameterList();

    // 属性
    QString m_analysisMethod;
    QString m_fittingModel;
    double m_t0;
    double m_fwhm;
    QString m_analysisMode;
    QString m_exponentialModel;
    int m_modelParametersCount;
    QString m_modelAlgorithm;
    int m_iterations;
    double m_tolerance;
    double m_rangeFrom;
    double m_rangeTo;

    // 参数列表
    QMap<QString, ParameterInfo> m_parameters;

    // IRF数据
    QVector<double> m_irfData;
};

#endif // FITPARAMETERMODEL_H
