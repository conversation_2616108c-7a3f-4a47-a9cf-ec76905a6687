#ifndef FITMODELS_H
#define FITMODELS_H

#include "FitModelInterface.h"
#include <cmath>
#include <QVector>
#include <QString>
#include <QMap>
#include <QDebug>
#include <Eigen/Dense>
#include <random>

// 前向声明辅助函数
double calculateLogLikelihood(const QVector<double>& yObs, const QVector<double>& yFit);
double calculatePosterior(const QVector<double>& yObs, const QVector<double>& yFit,
                          const Eigen::VectorXd& params, const Eigen::VectorXd& priorMeans,
                          const Eigen::VectorXd& priorStdDevs);
QVector<Eigen::VectorXd> metropolisHastings(
    const QVector<double>& xData,
    const QVector<double>& yData,
    const Eigen::VectorXd& initialParams,
    const Eigen::VectorXd& priorMeans,
    const Eigen::VectorXd& priorStdDevs,
    int n,
    int numSamples,
    int burnIn,
    const QVector<double>& irfData = QVector<double>(),
    bool useConvolution = false);


// N指数衰减模型: y = Σ(Ai * exp(-x/τi)) + B
class NExponentialDecayModel : public FitModelInterface {
public:
    // 定义拟合算法类型
    enum FitAlgorithm {
        LeastSquares = 0,       // 最小二乘法
        MaximumLikelihood = 1,  // 最大似然估计
        BayesianAnalysis = 2    // 贝叶斯分析
    };

    NExponentialDecayModel(int n = 1);
    virtual ~NExponentialDecayModel() {}

    QString getName() const override { return "N指数衰减"; }
    QString getDescription() const override { return "N指数衰减模型: y = Σ(Ai * exp(-x/τi)) + B"; }
    QString getFormula() const override { return "y = Σ(Ai * exp(-x/τi)) + B"; }

    QVector<FitParameter> getParameters() const override;
    void setParameter(const QString& name, double value) override;
    void setParameterFixed(const QString& name, bool fixed) override;
    void setParameterRange(const QString& name, double min, double max) override;

    FitResult fit(const QVector<double>& x, const QVector<double>& y,
                  double xMin = 0, double xMax = 0) override;
    // 直接进行卷积拟合，不通过尾部拟合获取初始参数
    FitResult fitConvolution(const QVector<double>& x, const QVector<double>& y,
                  double xMin = 0, double xMax = 0);

    // 执行卷积操作的辅助函数
    QVector<double> convolveWithIRF(const QVector<double>& model, const QVector<double>& irf) const;

    QVector<double> calculate(const QVector<double>& x) override;

    void setN(int n);

    // 设置拟合算法
    void setFitAlgorithm(FitAlgorithm algorithm) { m_fitAlgorithm = algorithm; }
    FitAlgorithm getFitAlgorithm() const { return m_fitAlgorithm; }

    // IRF数据相关方法
    void setIRFData(const QVector<double>& irfData) { m_irfData = irfData; m_useConvolution = true; }
    QVector<double> getIRFData() const { return m_irfData; }
    bool hasIRFData() const { return !m_irfData.isEmpty(); }
    void clearIRFData() { m_irfData.clear(); m_useConvolution = false; }

    // 时间偏移相关方法
    void setTimeOffset(double offset) { m_timeOffset = offset; }
    double getTimeOffset() const { return m_timeOffset; }

    // 自动估计IRF时间偏移
    double estimateTimeOffset(const QVector<double>& xData);

private:
    // 各种拟合方法的实现
    Eigen::VectorXd fitLeastSquares(const QVector<double>& xData, const QVector<double>& yData, Eigen::VectorXd& initialParams);
    Eigen::VectorXd fitMaximumLikelihood(const QVector<double>& xData, const QVector<double>& yData, Eigen::VectorXd& initialParams);
    Eigen::VectorXd fitBayesian(const QVector<double>& xData, const QVector<double>& yData, Eigen::VectorXd& initialParams);

    QMap<QString, FitParameter> m_parameters;
    int m_n;
    FitAlgorithm m_fitAlgorithm = LeastSquares; // 默认使用最小二乘法
    QVector<double> m_irfData;  // IRF数据
    bool m_useConvolution = false; // 是否使用卷积拟合
    double m_timeOffset = 0.0;  // IRF时间偏移量
};

// 高斯模型: y = A * exp(-(x-μ)²/(2*σ²)) + B
class GaussianModel : public FitModelInterface {
public:
    GaussianModel();
    virtual ~GaussianModel() {}

    QString getName() const override { return "高斯分布"; }
    QString getDescription() const override { return "高斯分布模型: y = A * exp(-(x-μ)²/(2*σ²)) + B"; }
    QString getFormula() const override { return "y = A * exp(-(x-μ)²/(2*σ²)) + B"; }

    QVector<FitParameter> getParameters() const override;
    void setParameter(const QString& name, double value) override;
    void setParameterFixed(const QString& name, bool fixed) override;
    void setParameterRange(const QString& name, double min, double max) override;

    FitResult fit(const QVector<double>& x, const QVector<double>& y,
                  double xMin = 0, double xMax = 0) override;
    QVector<double> calculate(const QVector<double>& x) override;

private:
    QMap<QString, FitParameter> m_parameters;
};

// 洛伦兹模型: y = A * γ²/((x-x₀)² + γ²) + B
class LorentzianModel : public FitModelInterface {
public:
    LorentzianModel();
    virtual ~LorentzianModel() {}

    QString getName() const override { return "洛伦兹分布"; }
    QString getDescription() const override { return "洛伦兹分布模型: y = A * γ²/((x-x₀)² + γ²) + B"; }
    QString getFormula() const override { return "y = A * γ²/((x-x₀)² + γ²) + B"; }

    QVector<FitParameter> getParameters() const override;
    void setParameter(const QString& name, double value) override;
    void setParameterFixed(const QString& name, bool fixed) override;
    void setParameterRange(const QString& name, double min, double max) override;

    FitResult fit(const QVector<double>& x, const QVector<double>& y,
                  double xMin = 0, double xMax = 0) override;
    QVector<double> calculate(const QVector<double>& x) override;

private:
    QMap<QString, FitParameter> m_parameters;
};

#endif // FITMODELS_H
