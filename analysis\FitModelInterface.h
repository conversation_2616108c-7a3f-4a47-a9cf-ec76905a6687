#ifndef FITMODELINTERFACE_H
#define FITMODELINTERFACE_H

#include <QVector>
#include <QString>
#include <QMap>
#include <QVariant>

// 拟合参数结构体
struct FitParameter {
    QString name;        // 参数名称
    double value;        // 参数值
    double minValue;     // 最小值
    double maxValue;     // 最大值
    bool fixed;          // 是否固定（不参与拟合）

    FitParameter() : value(0), minValue(0), maxValue(0), fixed(false) {}

    FitParameter(const QString& n, double v, double min, double max, bool f = false)
        : name(n), value(v), minValue(min), maxValue(max), fixed(f) {}
};

// 拟合结果结构体
struct FitResult {
    QVector<double> xData;       // X轴数据
    QVector<double> yData;       // Y轴数据（拟合结果）
    QVector<double> originalY;   // 原始Y轴数据
    QMap<QString, double> parameters;  // 拟合后的参数值
    double chiSquare;            // 拟合优度（卡方值）
    double rSquare;              // 决定系数
    QString errorMessage;        // 错误信息
    bool success;                // 拟合是否成功

    FitResult() : chiSquare(0), rSquare(0), success(false) {}
};

// 拟合模型接口
class FitModelInterface {
public:
    virtual ~FitModelInterface() {}

    // 获取模型名称
    virtual QString getName() const = 0;

    // 获取模型描述
    virtual QString getDescription() const = 0;

    // 获取模型参数列表
    virtual QVector<FitParameter> getParameters() const = 0;

    // 设置参数值
    virtual void setParameter(const QString& name, double value) = 0;

    // 设置参数是否固定
    virtual void setParameterFixed(const QString& name, bool fixed) = 0;

    // 设置参数范围
    virtual void setParameterRange(const QString& name, double min, double max) = 0;

    // 执行拟合
    virtual FitResult fit(const QVector<double>& x, const QVector<double>& y,
                          double xMin = 0, double xMax = 0) = 0;

    // 根据参数计算y值（不执行拟合，直接使用当前参数计算）
    virtual QVector<double> calculate(const QVector<double>& x) = 0;

    // 获取拟合函数的表达式（用于显示）
    virtual QString getFormula() const = 0;
};

#endif // FITMODELINTERFACE_H
