#include "IRFImporter.h"
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QDebug>
#include <QMessageBox>
#include <QDataStream>
#include <QFileDialog>
#include <QRegularExpression>
#include <cmath>
#include "analysis/FitParameterModel.h"

// 初始化静态实例
IRFImporter* IRFImporter::instance = nullptr;

IRFImporter* IRFImporter::getInstance()
{
    if (!instance) {
        instance = new IRFImporter();
    }
    return instance;
}

IRFImporter::IRFImporter(QObject* parent)
    : QObject(parent)
{
}

IRFImporter::~IRFImporter()
{
    // 清理资源
}

bool IRFImporter::importIRFFromFile(const QString& filePath)
{
    if (filePath.isEmpty()) {
        qWarning() << "IRFImporter: Empty file path";
        return false;
    }

    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    bool success = false;

    // 根据文件扩展名选择适当的读取方法
    if (extension == "csv") {
        success = readCSVFile(filePath);
    } else if (extension == "txt") {
        success = readTXTFile(filePath);
    } else if (extension == "dat") {
        success = readDATFile(filePath);
    } else {
        qWarning() << "IRFImporter: Unsupported file format:" << extension;
        return false;
    }

    if (success) {
        m_filePath = filePath;

        // 计算导入IRF的统计信息
        double sum = 0.0;
        double maxValue = 0.0;
        double minValue = m_irfData.isEmpty() ? 0.0 : m_irfData[0];
        for (double value : m_irfData) {
            sum += value;
            if (value > maxValue) maxValue = value;
            if (value < minValue) minValue = value;
        }

        m_importedIRFData = m_irfData;

        // 更新FitParameterModel中的IRF数据
        FitParameterModel* model = FitParameterModel::getInstance();
        model->setIRFData(m_irfData);

        // 发出信号通知IRF数据已导入
        emit irfDataImported();

        qDebug() << "IRFImporter: Successfully imported IRF data from" << filePath
                 << "with" << m_irfData.size() << "data points";
        qDebug() << "导入IRF调试信息 - sum:" << sum << ", max:" << maxValue << ", min:" << minValue;
        qDebug() << "导入IRF数据类型: double (从文件读取)";
    }

    return success;
}

bool IRFImporter::generateGaussianIRF(double t0, double fwhm, int dataPoints)
{
    if (fwhm <= 0 || dataPoints <= 0) {
        qWarning() << "IRFImporter: Invalid parameters for Gaussian IRF generation";
        return false;
    }

    m_userDefinedIRFData.clear();

    // 将FWHM转换为标准差 (sigma = FWHM / (2 * sqrt(2 * ln(2))))
    double sigma = fwhm / (2.0 * std::sqrt(2.0 * std::log(2.0)));

    // 生成高斯函数数据（简单的一维数组，索引作为时间）
    double sum = 0.0;
    double maxValue = 0.0;
    for (int i = 0; i < dataPoints; ++i) {
        double t = static_cast<double>(i) - t0;  // 以索引为时间，t0为中心偏移
        double exponent = -std::pow(t, 2) / (2.0 * std::pow(sigma, 2));
        double value = std::exp(exponent);
        m_userDefinedIRFData.append(value);
        sum += value;
        if (value > maxValue) maxValue = value;
    }

    // 更新FitParameterModel中的IRF数据（与导入IRF保持一致）
    FitParameterModel* model = FitParameterModel::getInstance();
    model->setIRFData(m_userDefinedIRFData);

    // 发出信号通知用户定义的IRF数据已生成
    emit userDefinedIRFGenerated();

    qDebug() << "IRFImporter: Successfully generated Gaussian IRF with t0=" << t0
             << ", FWHM=" << fwhm << ", data points=" << dataPoints;
    qDebug() << "模拟IRF调试信息 - sum:" << sum << ", max:" << maxValue;
    qDebug() << "模拟IRF数据类型: double (计算生成)";

    return true;
}

void IRFImporter::setUserDefinedIRF(const QVector<double>& irfData)
{
    m_userDefinedIRFData = irfData;

    // 更新FitParameterModel中的IRF数据（与导入IRF保持一致）
    FitParameterModel* model = FitParameterModel::getInstance();
    model->setIRFData(m_userDefinedIRFData);

    emit userDefinedIRFGenerated();
}

QVector<double> IRFImporter::getIRFData() const
{
    // 优先返回用户定义的IRF数据，如果没有则返回导入的IRF数据
    if (!m_userDefinedIRFData.isEmpty()) {
        return m_userDefinedIRFData;
    }
    return m_importedIRFData;
}

bool IRFImporter::hasIRFData() const
{
    return !m_importedIRFData.isEmpty() || !m_userDefinedIRFData.isEmpty();
}

bool IRFImporter::hasImportedIRFData() const
{
    return !m_importedIRFData.isEmpty();
}

bool IRFImporter::hasUserDefinedIRFData() const
{
    return !m_userDefinedIRFData.isEmpty();
}

void IRFImporter::clearIRFData()
{
    m_importedIRFData.clear();
    m_userDefinedIRFData.clear();
    m_filePath.clear();

    // 清除FitParameterModel中的IRF数据
    FitParameterModel* model = FitParameterModel::getInstance();
    model->clearIRFData();
}

void IRFImporter::clearImportedIRFData()
{
    m_importedIRFData.clear();
    m_filePath.clear();
}

void IRFImporter::clearUserDefinedIRFData()
{
    m_userDefinedIRFData.clear();
}

bool IRFImporter::readCSVFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "IRFImporter: Error opening CSV file:" << filePath;
        return false;
    }

    QTextStream in(&file);
    m_irfData.clear();

    // 读取CSV文件
    bool isFirstRow = true; // 跳过第一行（标题行）
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (isFirstRow) {
            isFirstRow = false;
            continue;
        }

        QStringList values = line.split(",");
        if (values.size() < 2) {
            continue; // 跳过列数不足的行
        }

        // 假设第二列是IRF数据
        bool ok;
        double value = values[1].toDouble(&ok);
        if (ok) {
            m_irfData.append(value);
        }
    }

    file.close();
    return !m_irfData.isEmpty();
}

bool IRFImporter::readTXTFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "IRFImporter: Error opening TXT file:" << filePath;
        return false;
    }

    QTextStream in(&file);
    m_irfData.clear();

    // 读取TXT文件
    while (!in.atEnd()) {
        QString line = in.readLine();
        line = line.trimmed();

        // 跳过空行和注释行
        if (line.isEmpty() || line.startsWith("#") || line.startsWith("//")) {
            continue;
        }

        // 尝试将行解析为单个数值
        bool ok;
        double value = line.toDouble(&ok);
        if (ok) {
            m_irfData.append(value);
        } else {
            // 如果不是单个数值，尝试解析为空格分隔的多个数值
            QStringList values = line.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
            if (values.size() > 0) {
                // 使用第一列作为IRF数据
                value = values[0].toDouble(&ok);
                if (ok) {
                    m_irfData.append(value);
                }
            }
        }
    }

    file.close();
    return !m_irfData.isEmpty();
}

bool IRFImporter::readDATFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "IRFImporter: Error opening DAT file:" << filePath;
        return false;
    }

    // 尝试以二进制格式读取
    QDataStream in(&file);
    m_irfData.clear();

    // 读取DAT文件（假设是二进制格式的double数组）
    while (!in.atEnd()) {
        double value;
        in >> value;
        if (in.status() == QDataStream::Ok) {
            m_irfData.append(value);
        } else {
            break;
        }
    }

    // 如果二进制读取失败，尝试以文本格式读取
    if (m_irfData.isEmpty()) {
        file.seek(0);
        QTextStream textIn(&file);
        while (!textIn.atEnd()) {
            QString line = textIn.readLine().trimmed();
            if (line.isEmpty() || line.startsWith("#") || line.startsWith("//")) {
                continue;
            }

            bool ok;
            double value = line.toDouble(&ok);
            if (ok) {
                m_irfData.append(value);
            }
        }
    }

    file.close();
    return !m_irfData.isEmpty();
}
