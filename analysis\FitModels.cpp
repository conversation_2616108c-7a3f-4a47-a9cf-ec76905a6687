#include "FitModels.h"
#include "FitParameterModel.h"
#include <QDebug>
#include <algorithm>
#include <numeric>
#include <Eigen/Dense>
#include <unsupported/Eigen/NonLinearOptimization>
#include <boost/math/distributions/normal.hpp>
#include <boost/math/distributions/poisson.hpp>
#include <boost/math/distributions/gamma.hpp>
#include <boost/math/statistics/univariate_statistics.hpp>
#include <boost/random/mersenne_twister.hpp>
#include <boost/random/normal_distribution.hpp>
#include <boost/random/uniform_real_distribution.hpp>
#include <random>
#include <chrono>
#include <nlopt.h>



// 辅助函数：计算R²值
double calculateRSquared(const QVector<double>& yObs, const QVector<double>& yFit) {
    if (yObs.size() != yFit.size() || yObs.isEmpty()) {
        return 0.0;
    }

    // 计算观测值的平均值
    double yMean = std::accumulate(yObs.begin(), yObs.end(), 0.0) / yObs.size();

    // 计算总平方和 (SST)
    double sst = 0.0;
    for (const double& y : yObs) {
        sst += (y - yMean) * (y - yMean);
    }

    // 计算残差平方和 (SSE)
    double sse = 0.0;
    for (int i = 0; i < yObs.size(); ++i) {
        sse += (yObs[i] - yFit[i]) * (yObs[i] - yFit[i]);
    }

    // 计算R²
    if (sst == 0.0) {
        return 0.0; // 避免除以零
    }
    return 1.0 - (sse / sst);
}

// 辅助函数：计算卡方值
double calculateChiSquare(const QVector<double>& yObs, const QVector<double>& yFit) {
    if (yObs.size() != yFit.size() || yObs.isEmpty()) {
        return 0.0;
    }

    double chiSquare = 0.0;
    for (int i = 0; i < yObs.size(); ++i) {
        // 避免除以零，使用max(y, 1.0)作为权重
        double weight = std::max(yObs[i], 1.0);
        double diff = yObs[i] - yFit[i];
        chiSquare += (diff * diff) / weight;
    }

    return chiSquare / (yObs.size() - 1); // 自由度校正
}

// 辅助函数：计算对数似然值（用于最大似然估计）
double calculateLogLikelihood(const QVector<double>& yObs, const QVector<double>& yFit) {
    if (yObs.size() != yFit.size() || yObs.isEmpty()) {
        return -std::numeric_limits<double>::infinity();
    }

    double logLikelihood = 0.0;
    for (int i = 0; i < yObs.size(); ++i) {
        // 避免处理无效数据
        if (yFit[i] <= 0 || yObs[i] < 0) {
            continue; // 跳过无效数据点
        }

        // 对于荧光衰减曲线，通常使用泊松分布模型
        // 但我们直接使用泊松分布的对数似然公式，避免使用boost库的pdf计算
        // 泊松分布的对数似然公式：y * log(μ) - μ - log(y!)
        // 其中y是观测值，μ是预测值

        // 计算对数似然
        double observed = yObs[i];
        double expected = yFit[i];

        // 使用Stirling近似计算log(y!)，避免大数值问题
        double logFactorial = 0.0;
        if (observed > 0) {
            // 使用Stirling公式近似log(n!)：log(n!) ≈ n*log(n) - n + 0.5*log(2*π*n)
            if (observed > 10) {
                logFactorial = observed * std::log(observed) - observed + 0.5 * std::log(2 * M_PI * observed);
            } else {
                // 对于小值，直接使用标准库的lgamma函数
                logFactorial = std::lgamma(observed + 1);
            }
        }

        // 计算泊松分布的对数似然
        logLikelihood += observed * std::log(expected) - expected - logFactorial;
    }

    return logLikelihood;
}

// 辅助函数：计算后验概率（用于贝叶斯分析）
double calculatePosterior(const QVector<double>& yObs, const QVector<double>& yFit,
                          const Eigen::VectorXd& params, const Eigen::VectorXd& priorMeans,
                          const Eigen::VectorXd& priorStdDevs) {
    // 计算似然
    double logLikelihood = calculateLogLikelihood(yObs, yFit);

    // 计算先验概率的对数
    double logPrior = 0.0;
    for (int i = 0; i < params.size(); ++i) {
        // 使用正态分布作为先验
        try {
            boost::math::normal_distribution<double> prior(priorMeans(i), priorStdDevs(i));
            double pdf = boost::math::pdf(prior, params(i));
            if (pdf > 0) {
                logPrior += std::log(pdf);
            }
        } catch (const std::exception& e) {
            qDebug() << "计算先验概率时出错:" << e.what() << "at i=" << i << "param=" << params(i);
            // 如果参数超出了合理范围，返回负无穷
            return -std::numeric_limits<double>::infinity();
        }
    }

    // 后验概率正比于似然乘以先验
    return logLikelihood + logPrior;
}

// 辅助函数：使用Metropolis-Hastings算法进行贝叶斯分析
QVector<Eigen::VectorXd> metropolisHastings(
    const QVector<double>& xData,
    const QVector<double>& yData,
    const Eigen::VectorXd& initialParams,
    const Eigen::VectorXd& priorMeans,
    const Eigen::VectorXd& priorStdDevs,
    int n,  // 指数项数量
    int numSamples,
    int burnIn,
    const QVector<double>& irfData,
    bool useConvolution) {

    // 初始化随机数生成器
    unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();
    boost::random::mt19937 rng(seed);

    // 卷积辅助函数，考虑时间偏移
    auto convolveWithIRF = [&](const QVector<double>& model) -> QVector<double> {
        if (model.isEmpty() || irfData.isEmpty() || !useConvolution) {
            return model; // 如果任一输入为空或未启用卷积，直接返回原始模型
        }

        int modelSize = model.size();
        int irfSize = irfData.size();
        QVector<double> result(modelSize, 0.0);

        // 找到IRF的峰值位置
        int irfPeakIndex = 0;
        double irfMaxVal = irfData[0];
        for (int i = 1; i < irfSize; ++i) {
            if (irfData[i] > irfMaxVal) {
                irfMaxVal = irfData[i];
                irfPeakIndex = i;
            }
        }

        // 使用IRF峰值位置作为时间偏移的基准
        int timeOffset = irfPeakIndex;

        qDebug() << "贝叶斯卷积 - IRF峰值位置:" << irfPeakIndex << "使用时间偏移:" << timeOffset;

        // 执行离散卷积，考虑时间偏移
        for (int i = 0; i < modelSize; ++i) {
            double sum = 0.0;
            for (int j = 0; j < irfSize; ++j) {
                // 计算考虑时间偏移的索引
                int k = i - j + timeOffset;
                if (k >= 0 && k < modelSize) {
                    sum += model[k] * irfData[j];
                }
            }
            result[i] = sum;
        }

        return result;
    };

    // 存储采样结果
    QVector<Eigen::VectorXd> samples;

    // 当前参数和后验概率
    Eigen::VectorXd currentParams = initialParams;

    // 计算当前参数下的拟合值
    QVector<double> currentFit;
    for (int i = 0; i < xData.size(); i++) {
        double sum = currentParams(2*n); // B值
        for(int j=0; j<n; ++j) {
            double A = currentParams(2*j);
            double tau = currentParams(2*j+1);
            sum += A * exp(-xData[i]/tau);
        }
        currentFit.append(sum);
    }

    // 如果启用卷积，将拟合值与IRF进行卷积
    if (useConvolution && !irfData.isEmpty()) {
        currentFit = convolveWithIRF(currentFit);
    }

    // 计算当前后验概率
    double currentPosterior = calculatePosterior(yData, currentFit, currentParams, priorMeans, priorStdDevs);

    // 设置提议分布的标准差（可以根据参数范围调整）
    Eigen::VectorXd proposalStdDevs = priorStdDevs * 0.1;

    // 主循环
    for (int i = 0; i < numSamples + burnIn; i++) {
        // 生成提议参数
        Eigen::VectorXd proposedParams = currentParams;
        for (int j = 0; j < proposedParams.size(); j++) {
            boost::random::normal_distribution<double> dist(0, proposalStdDevs(j));
            proposedParams(j) += dist(rng);
        }

        // 确保参数在合理范围内
        for (int j = 0; j < n; j++) {
            proposedParams(2*j) = std::max(0.0, proposedParams(2*j)); // A_j >= 0
            proposedParams(2*j+1) = std::max(0.1, proposedParams(2*j+1)); // tau_j >= 0.1
        }

        // 计算提议参数下的拟合值
        QVector<double> proposedFit;
        for (int j = 0; j < xData.size(); j++) {
            double sum = proposedParams(2*n); // B值
            for(int k=0; k<n; ++k) {
                double A = proposedParams(2*k);
                double tau = proposedParams(2*k+1);
                sum += A * exp(-xData[j]/tau);
            }
            proposedFit.append(sum);
        }

        // 如果启用卷积，将提议拟合值与IRF进行卷积
        if (useConvolution && !irfData.isEmpty()) {
            proposedFit = convolveWithIRF(proposedFit);
        }

        // 计算提议参数的后验概率
        double proposedPosterior = calculatePosterior(yData, proposedFit, proposedParams, priorMeans, priorStdDevs);

        // 计算接受概率
        double acceptanceRatio = std::exp(proposedPosterior - currentPosterior);
        boost::random::uniform_real_distribution<double> uniform(0, 1);

        // 接受或拒绝提议
        if (uniform(rng) < acceptanceRatio) {
            currentParams = proposedParams;
            currentFit = proposedFit;
            currentPosterior = proposedPosterior;
        }

        // 保存样本（跳过burn-in阶段）
        if (i >= burnIn) {
            samples.append(currentParams);
        }
    }

    return samples;
}


// 定义用于非线性优化的函数对象
struct ExponentialFunctor {
    const QVector<double>& x;
    const QVector<double>& y;

    ExponentialFunctor(const QVector<double>& x_, const QVector<double>& y_)
        : x(x_), y(y_) {}

    int operator()(const Eigen::VectorXd& params, Eigen::VectorXd& fvec) const {
        double A = params(0);
        double tau = params(1);
        double B = params(2);

        for (int i = 0; i < x.size(); i++) {
            fvec(i) = y[i] - (A * exp(-x[i]/tau) + B);
        }
        return 0;
    }

    int df(const Eigen::VectorXd& params, Eigen::MatrixXd& fjac) const {
        double A = params(0);
        double tau = params(1);

        for (int i = 0; i < x.size(); i++) {
            // dF/dA
            fjac(i,0) = -exp(-x[i]/tau);
            // dF/dtau
            fjac(i,1) = -A * exp(-x[i]/tau) * x[i]/(tau*tau);
            // dF/dB
            fjac(i,2) = -1.0;
        }
        return 0;
    }

    int inputs() const { return 3; } // 参数个数：A, tau, B
    int values() const { return x.size(); } // 数据点个数
};


// 最小二乘法拟合
Eigen::VectorXd NExponentialDecayModel::fitLeastSquares(const QVector<double>& xData, const QVector<double>& yData, Eigen::VectorXd& initialParams) {
    // 定义动态参数优化函数
    struct NExponentialFunctor {
        int m_n;
        const QVector<double>& x;
        const QVector<double>& y;
        const QVector<double>& irf;
        bool useConvolution;


        // 添加IRF数据和卷积标志到构造函数
        NExponentialFunctor(int n, const QVector<double>& x_, const QVector<double>& y_,
                            const QVector<double>& irf_, bool useConvolution_)
            : m_n(n), x(x_), y(y_), irf(irf_), useConvolution(useConvolution_) {}

        // 执行卷积操作的辅助函数，考虑时间偏移
        QVector<double> convolveWithIRF(const QVector<double>& model) const {
            if (model.isEmpty() || irf.isEmpty()) {
                return model; // 如果任一输入为空，直接返回原始模型
            }

            int modelSize = model.size();
            int irfSize = irf.size();
            QVector<double> result(modelSize, 0.0);

            // 找到IRF的峰值位置
            int irfPeakIndex = 0;
            double irfMaxVal = irf[0];
            for (int i = 1; i < irfSize; ++i) {
                if (irf[i] > irfMaxVal) {
                    irfMaxVal = irf[i];
                    irfPeakIndex = i;
                }
            }

            // 使用IRF峰值位置作为时间偏移的基准
            int timeOffset = irfPeakIndex;

            qDebug() << "IRF峰值位置:" << irfPeakIndex << "使用时间偏移:" << timeOffset;

            // 执行离散卷积，考虑时间偏移
            for (int i = 0; i < modelSize; ++i) {
                double sum = 0.0;
                for (int j = 0; j < irfSize; ++j) {
                    // 计算考虑时间偏移的索引
                    int k = i - j + timeOffset;
                    if (k >= 0 && k < modelSize) {
                        sum += model[k] * irf[j];
                    }
                }
                result[i] = sum;
            }

            return result;
        }

        int operator()(const Eigen::VectorXd& params, Eigen::VectorXd& fvec) const {
            double B = params(2*m_n);

            // 计算指数衰减模型
            QVector<double> modelValues(x.size(), B);
            for (int i = 0; i < x.size(); i++) {
                for(int j=0; j<m_n; ++j) {
                    double A = params(2*j);
                    double tau = params(2*j+1);
                    modelValues[i] += A * exp(-x[i]/tau);
                }
            }

            // 如果启用卷积，将模型与IRF进行卷积
            if (useConvolution && !irf.isEmpty()) {
                modelValues = convolveWithIRF(modelValues);
            }

            // 计算数据拟合残差
            for (int i = 0; i < x.size(); i++) {
                fvec(i) = y[i] - modelValues[i];
            }

            return 0;
        }

        int df(const Eigen::VectorXd& params, Eigen::MatrixXd& fjac) const {
            double B = params(2*m_n);

            // 数据拟合残差的雅可比矩阵
            for (int i = 0; i < x.size(); i++) {
                for(int j=0; j<m_n; ++j) {
                    double A = params(2*j);
                    double tau = params(2*j+1);
                    double exp_term = exp(-x[i]/tau);

                    // 对A的导数
                    fjac(i, 2*j) = -exp_term;

                    // 对tau的导数
                    fjac(i, 2*j+1) = -A * exp_term * x[i]/(tau*tau);
                }
                // 对B的导数
                fjac(i, 2*m_n) = -1.0;
            }

            return 0;
        }

        int inputs() const { return 2*m_n +1; }
        int values() const {
            // 对于三指数及以上模型，添加额外的虚拟残差用于正则化
            if (m_n >= 3) {
                // 数据点 + 振幅正则化项 + 时间常数上限约束 + 时间常数排序约束
                return x.size() + m_n + m_n + (m_n - 1);
            }
            return x.size();
        }
    };

    // 执行优化，传递IRF数据和卷积标志
    NExponentialFunctor functor(m_n, xData, yData, m_irfData, m_useConvolution);
    Eigen::LevenbergMarquardt<NExponentialFunctor> lm(functor);

    // 根据模型复杂度调整优化参数
    // 使用FitParameterModel中的迭代次数
    int iterationCount = FitParameterModel::getInstance()->iterations();
    lm.parameters.maxfev = iterationCount; // 使用用户设置的迭代次数
    qDebug() << "最小二乘法 - 使用迭代次数:" << iterationCount;

    // 设置收敛容差
    double tolerance = FitParameterModel::getInstance()->tolerance();
    lm.parameters.xtol = tolerance;   // 参数收敛容差
    lm.parameters.ftol = tolerance;   // 函数值收敛容差
    qDebug() << "最小二乘法 - 使用容差:" << tolerance;

    // 根据模型复杂度调整其他优化参数
    if (m_n > 2) {
        lm.parameters.factor = 0.1;   // 减小初始步长，使优化更精细
    }

    // 执行优化前确保参数在合理范围内
    for (int i = 0; i < m_n; i++) {
        initialParams(2*i) = std::max(1.0, initialParams(2*i));     // A_i > 0
        initialParams(2*i+1) = std::max(0.1, initialParams(2*i+1)); // tau_i > 0.1
    }

    // 执行优化
    qDebug() << "开始最小二乘法优化...";
    int status = lm.minimize(initialParams);

    // 检查优化结果
    if(status != Eigen::LevenbergMarquardtSpace::Status::CosinusTooSmall &&
        status != Eigen::LevenbergMarquardtSpace::Status::RelativeReductionTooSmall) {
        qDebug() << "最小二乘法优化未收敛，状态码:" << status;
    } else {
        qDebug() << "最小二乘法优化成功";
    }

    return initialParams;
}

// 最大似然估计拟合
Eigen::VectorXd NExponentialDecayModel::fitMaximumLikelihood(const QVector<double>& xData, const QVector<double>& yData, Eigen::VectorXd& initialParams) {
    // 创建一个新的参数向量，用于最大似然估计
    Eigen::VectorXd mleParams = initialParams;

    // 定义最大似然估计的目标函数结构体，使用Levenberg-Marquardt算法
    struct MLEFunctor {
        int m_n;
        const QVector<double>& x;
        const QVector<double>& y;
        const QVector<double>& irf;
        bool useConvolution;

        // 添加IRF数据和卷积标志到构造函数
        MLEFunctor(int n, const QVector<double>& x_, const QVector<double>& y_,
                   const QVector<double>& irf_, bool useConvolution_)
            : m_n(n), x(x_), y(y_), irf(irf_), useConvolution(useConvolution_) {}

        // 执行卷积操作的辅助函数，考虑时间偏移
        QVector<double> convolveWithIRF(const QVector<double>& model) const {
            if (model.isEmpty() || irf.isEmpty()) {
                return model; // 如果任一输入为空，直接返回原始模型
            }

            int modelSize = model.size();
            int irfSize = irf.size();
            QVector<double> result(modelSize, 0.0);

            // 找到IRF的峰值位置
            int irfPeakIndex = 0;
            double irfMaxVal = irf[0];
            for (int i = 1; i < irfSize; ++i) {
                if (irf[i] > irfMaxVal) {
                    irfMaxVal = irf[i];
                    irfPeakIndex = i;
                }
            }

            // 使用IRF峰值位置作为时间偏移的基准
            int timeOffset = irfPeakIndex;

            qDebug() << "MLE卷积 - IRF峰值位置:" << irfPeakIndex << "使用时间偏移:" << timeOffset;

            // 执行离散卷积，考虑时间偏移
            for (int i = 0; i < modelSize; ++i) {
                double sum = 0.0;
                for (int j = 0; j < irfSize; ++j) {
                    // 计算考虑时间偏移的索引
                    int k = i - j + timeOffset;
                    if (k >= 0 && k < modelSize) {
                        sum += model[k] * irf[j];
                    }
                }
                result[i] = sum;
            }

            return result;
        }

        // 计算残差向量，用于Levenberg-Marquardt优化
        int operator()(const Eigen::VectorXd& params, Eigen::VectorXd& fvec) const {
            double B = params(2*m_n);

            // 计算指数衰减模型
            QVector<double> modelValues(x.size(), B);
            for (int i = 0; i < x.size(); i++) {
                for(int j=0; j<m_n; ++j) {
                    double A = params(2*j);
                    double tau = params(2*j+1);
                    modelValues[i] += A * exp(-x[i]/tau);
                }
            }

            // 如果启用卷积，将模型与IRF进行卷积
            if (useConvolution && !irf.isEmpty()) {
                modelValues = convolveWithIRF(modelValues);
            }

            // 对于最大似然估计，我们使用修正的残差计算
            for (int i = 0; i < x.size(); i++) {
                double observed = y[i];
                double expected = modelValues[i];

                // 避免除以零或负值
                if (expected <= 0) expected = 1e-6;

                // 使用泊松分布的特性：方差等于均值
                // 标准化残差 = (观测值 - 预测值) / sqrt(预测值)
                fvec(i) = (observed - expected) / sqrt(expected);
            }
            return 0;
        }

        // 计算雅可比矩阵，用于Levenberg-Marquardt优化
        // 这是Eigen的LevenbergMarquardt实现所需要的
        int df(const Eigen::VectorXd& params, Eigen::MatrixXd& fjac) const {
            // 注意：当启用卷积时，雅可比矩阵的计算会变得非常复杂
            // 目前使用数值微分的方式来处理卷积情况
            if (useConvolution && !irf.isEmpty()) {
                // 对于卷积情况，使用数值微分计算雅可比矩阵
                const double h = 1e-8; // 数值微分步长
                Eigen::VectorXd fvec_plus(x.size()), fvec_minus(x.size());
                Eigen::VectorXd params_plus = params, params_minus = params;

                for (int j = 0; j < params.size(); ++j) {
                    // 计算 f(x + h)
                    params_plus(j) = params(j) + h;
                    operator()(params_plus, fvec_plus);

                    // 计算 f(x - h)
                    params_minus(j) = params(j) - h;
                    operator()(params_minus, fvec_minus);

                    // 计算数值导数
                    for (int i = 0; i < x.size(); ++i) {
                        fjac(i, j) = (fvec_plus(i) - fvec_minus(i)) / (2.0 * h);
                    }

                    // 恢复参数值
                    params_plus(j) = params(j);
                    params_minus(j) = params(j);
                }
            } else {
                // 对于非卷积情况，使用解析导数
                for (int i = 0; i < x.size(); i++) {
                    // 计算当前点的拟合值
                    double yFit = params(2*m_n); // B值
                    for(int j=0; j<m_n; ++j) {
                        double A = params(2*j);
                        double tau = params(2*j+1);
                        yFit += A * exp(-x[i]/tau);
                    }

                    // 避免除以零或负值
                    if (yFit <= 0) yFit = 1e-6;
                    double sqrtYFit = sqrt(yFit);

                    // 对于B参数，偏导数始终为-1/sqrt(expected)
                    fjac(i, 2*m_n) = -1.0 / sqrtYFit;

                    // 对于其他参数，计算偏导数
                    for(int j=0; j<m_n; ++j) {
                        double A = params(2*j);
                        double tau = params(2*j+1);
                        double exp_term = exp(-x[i]/tau);

                        // 偏导数计算
                        fjac(i, 2*j) = -exp_term / sqrtYFit;
                        fjac(i, 2*j+1) = -A * x[i] * exp_term / (tau * tau * sqrtYFit);
                    }
                }
            }
            return 0;
        }

        // 返回输入向量的大小（参数个数）
        int inputs() const { return 2*m_n + 1; }

        // 返回输出向量的大小（数据点个数）
        int values() const { return x.size(); }
    };

    // 创建函数对象，传递IRF数据和卷积标志
    MLEFunctor functor(m_n, xData, yData, m_irfData, m_useConvolution);

    // 使用Levenberg-Marquardt算法进行优化
    Eigen::LevenbergMarquardt<MLEFunctor> lm(functor);

    // 设置优化参数
    int iterationCount = FitParameterModel::getInstance()->iterations();
    lm.parameters.maxfev = iterationCount;  // 使用用户设置的最大函数评估次数
    qDebug() << "最大似然估计 - 使用迭代次数:" << iterationCount;

    double tolerance = FitParameterModel::getInstance()->tolerance();
    lm.parameters.xtol = tolerance;   // 参数收敛容差
    lm.parameters.ftol = tolerance;   // 函数值收敛容差
    qDebug() << "最大似然估计 - 使用容差:" << tolerance;

    // 执行优化前确保参数在合理范围内
    for (int i = 0; i < m_n; i++) {
        mleParams(2*i) = std::max(1.0, mleParams(2*i));     // A_i > 0
        mleParams(2*i+1) = std::max(0.1, mleParams(2*i+1)); // tau_i > 0.1
    }

    // 执行优化
    qDebug() << "开始MLE优化...";

    try {
        // 使用try-catch块捕获可能的异常
        // 直接使用minimize方法，这是最简单和推荐的方式
        int status = lm.minimize(mleParams);

        // 每次迭代后确保参数在合理范围内
        for (int i = 0; i < m_n; i++) {
            mleParams(2*i) = std::max(1.0, mleParams(2*i));     // A_i > 0
            mleParams(2*i+1) = std::max(0.1, mleParams(2*i+1)); // tau_i > 0.1
        }

        // 检查优化结果
        if (status == Eigen::LevenbergMarquardtSpace::Status::CosinusTooSmall ||
            status == Eigen::LevenbergMarquardtSpace::Status::RelativeReductionTooSmall) {
            qDebug() << "MLE优化成功收敛，状态码:" << status;
        } else {
            qDebug() << "MLE优化未完全收敛，状态码:" << status;
        }
    }
    catch (const std::exception& e) {
        qDebug() << "MLE优化过程中发生异常:" << e.what();
        // 如果发生异常，保持初始参数不变
        mleParams = initialParams;
    }

    // 确保参数在合理范围内
    for (int i = 0; i < m_n; i++) {
        mleParams(2*i) = std::max(0.0, mleParams(2*i));     // A_i >= 0
        mleParams(2*i+1) = std::max(0.1, mleParams(2*i+1)); // tau_i >= 0.1
    }

    // 计算最终的对数似然值，用于调试和结果评估
    QVector<double> finalFit;

    // 计算指数衰减模型（包含背景噪声B）
    for (int i = 0; i < xData.size(); i++) {
        double sum = mleParams(2*m_n); // 包含背景值B
        for(int j=0; j<m_n; ++j) {
            double A = mleParams(2*j);
            double tau = mleParams(2*j+1);
            sum += A * exp(-xData[i]/tau);
        }
        finalFit.append(sum);
    }

    // 如果启用卷积，将最终拟合结果与IRF进行卷积
    if (m_useConvolution && !m_irfData.isEmpty()) {
        // 创建临时的MLEFunctor来使用其卷积函数
        MLEFunctor tempFunctor(m_n, xData, yData, m_irfData, m_useConvolution);
        finalFit = tempFunctor.convolveWithIRF(finalFit);
        qDebug() << "MLE最终结果已应用卷积";
    }

    double logLikelihood = calculateLogLikelihood(yData, finalFit);
    qDebug() << "最终对数似然值:" << logLikelihood;

    return mleParams;
}



// 贝叶斯分析拟合
Eigen::VectorXd NExponentialDecayModel::fitBayesian(const QVector<double>& xData, const QVector<double>& yData, Eigen::VectorXd& initialParams) {
    // 设置先验分布的均值和标准差
    Eigen::VectorXd priorMeans = initialParams;
    Eigen::VectorXd priorStdDevs = Eigen::VectorXd::Zero(initialParams.size());

    // 为每个参数设置合理的先验标准差
    for (int i = 0; i < m_n; i++) {
        priorStdDevs(2*i) = std::max(10.0, initialParams(2*i) * 0.2); // A_i的标准差
        priorStdDevs(2*i+1) = std::max(1.0, initialParams(2*i+1) * 0.2); // tau_i的标准差
    }
    priorStdDevs(2*m_n) = std::max(1.0, std::abs(initialParams(2*m_n) * 0.2)); // B的标准差

    // 执行贝叶斯分析（Metropolis-Hastings采样）
    qDebug() << "开始贝叶斯分析...";
    int iterationCount = FitParameterModel::getInstance()->iterations();
    double tolerance = FitParameterModel::getInstance()->tolerance();
    int numSamples = iterationCount / 2; // 使用迭代次数的一半作为样本数
    int burnIn = iterationCount / 10;    // 使用迭代次数的十分之一作为预热期
    qDebug() << "贝叶斯分析 - 使用迭代次数:" << iterationCount << "，样本数:" << numSamples << "，预热期:" << burnIn << "，容差:" << tolerance;

    // 传递IRF数据和卷积标志
    QVector<Eigen::VectorXd> samples = metropolisHastings(
        xData, yData, initialParams, priorMeans, priorStdDevs, m_n, numSamples, burnIn, m_irfData, m_useConvolution);
    qDebug() << "贝叶斯分析完成，获得" << samples.size() << "个样本";

    if (m_useConvolution && !m_irfData.isEmpty()) {
        qDebug() << "贝叶斯分析已启用卷积拟合";
    }

    // 计算参数的后验均值和标准差
    Eigen::VectorXd posteriorMeans = Eigen::VectorXd::Zero(initialParams.size());
    Eigen::VectorXd posteriorStdDevs = Eigen::VectorXd::Zero(initialParams.size());

    // 计算均值
    for (const auto& sample : samples) {
        posteriorMeans += sample;
    }
    posteriorMeans /= samples.size();

    return posteriorMeans;
}

// N指数衰减模型实现
NExponentialDecayModel::NExponentialDecayModel(int n) : m_n(n), m_useConvolution(false) {
    // 初始化参数
    for (int i = 1; i <= n; ++i) {
        m_parameters[QString("A%1").arg(i)] = FitParameter(QString("A%1").arg(i), 1000.0, 0.0, 1000000.0);
        m_parameters[QString("τ%1").arg(i)] = FitParameter(QString("τ%1").arg(i), 10.0, 0.1, 1000.0);
    }
    m_parameters["B"] = FitParameter("B", 0.0, -1000.0, 1000.0);
}

void NExponentialDecayModel::setN(int n) {
    if (n == m_n) return;

    // 保存现有参数的值和约束
    QMap<QString, FitParameter> oldParams = m_parameters;

    // 清空参数列表
    m_parameters.clear();
    m_n = n;

    // 重新初始化参数
    for (int i = 1; i <= n; ++i) {
        QString ai = QString("A%1").arg(i);
        QString taui = QString("τ%1").arg(i);

        m_parameters[ai] = oldParams.contains(ai) ? oldParams[ai] : FitParameter(ai, 1000.0, 0.0, 1000000.0);
        m_parameters[taui] = oldParams.contains(taui) ? oldParams[taui] : FitParameter(taui, 10.0, 0.1, 1000.0);
    }

    m_parameters["B"] = oldParams.contains("B") ? oldParams["B"] : FitParameter("B", 0.0, -1000.0, 1000.0);
}



QVector<FitParameter> NExponentialDecayModel::getParameters() const {
    QVector<FitParameter> params;
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        params.append(it.value());
    }
    return params;
}

void NExponentialDecayModel::setParameter(const QString& name, double value) {
    if (m_parameters.contains(name)) {
        m_parameters[name].value = value;
    }
}

void NExponentialDecayModel::setParameterFixed(const QString& name, bool fixed) {
    if (m_parameters.contains(name)) {
        m_parameters[name].fixed = fixed;
    }
}

void NExponentialDecayModel::setParameterRange(const QString& name, double min, double max) {
    if (m_parameters.contains(name)) {
        m_parameters[name].minValue = min;
        m_parameters[name].maxValue = max;
    }
}

// 自动估计IRF时间偏移
double NExponentialDecayModel::estimateTimeOffset(const QVector<double>& xData) {
    // 在尾部拟合中不需要时间偏移
    return 0.0;
}

// 执行卷积操作的辅助函数
QVector<double> NExponentialDecayModel::convolveWithIRF(const QVector<double>& model, const QVector<double>& irf) const {
    if (model.isEmpty() || irf.isEmpty()) {
        return model; // 如果任一输入为空，直接返回原始模型
    }

    int modelSize = model.size();
    int irfSize = irf.size();
    QVector<double> result(modelSize, 0.0);

    // 计算IRF的统计信息用于调试
    double irfSum = 0.0;
    double irfMax = 0.0;
    for (double value : irf) {
        irfSum += value;
        if (value > irfMax) irfMax = value;
    }

    // 计算原始模型的统计信息用于调试
    double modelSum = 0.0;
    double modelMax = 0.0;
    for (double value : model) {
        modelSum += value;
        if (value > modelMax) modelMax = value;
    }

    // 执行离散卷积
    for (int i = 0; i < modelSize; ++i) {
        double sum = 0.0;
        for (int j = 0; j < irfSize; ++j) {
            int k = i - j;
            if (k >= 0 && k < modelSize) {
                sum += model[k] * irf[j];
            }
        }
        result[i] = sum;
    }

    // 计算卷积后的统计信息用于调试
    double resultSum = 0.0;
    double resultMax = 0.0;
    for (double value : result) {
        resultSum += value;
        if (value > resultMax) resultMax = value;
    }

    qDebug() << "卷积调试信息:";
    qDebug() << "  IRF - sum:" << irfSum << ", max:" << irfMax << ", 数据点数:" << irfSize;
    qDebug() << "  原始模型 - sum:" << modelSum << ", max:" << modelMax << ", 数据点数:" << modelSize;
    qDebug() << "  卷积结果 - sum:" << resultSum << ", max:" << resultMax;
    qDebug() << "  振幅影响 - max比值:" << (modelMax > 0 ? resultMax/modelMax : 0)
             << ", sum比值:" << (modelSum > 0 ? resultSum/modelSum : 0);

    // 添加IRF积分值的影响提示
    if (irfSum > 1.1 || irfSum < 0.9) {
        qDebug() << "警告: IRF积分值为" << irfSum << "，偏离1.0，这会影响振幅拟合结果";
        qDebug() << "  如果使用不同IRF进行拟合，振幅结果可能相差" << irfSum << "倍";
    }

    return result;
}

QVector<double> NExponentialDecayModel::calculate(const QVector<double>& x) {
    QVector<double> result;

    // 获取拟合参数
    double B = m_parameters["B"].value;

    // 如果x为空，返回空结果
    if (x.isEmpty()) {
        return result;
    }

    // 获取参考点（用于归一化时间）
    double x0 = x.first();

    // 计算纯指数衰减模型（不包含背景噪声B）
    for (double xi : x) {
        double yi = 0.0; // 初始值不包含背景值B

        // 计算所有指数项的和
        for (int i = 1; i <= m_n; ++i) {
            double Ai = m_parameters[QString("A%1").arg(i)].value;
            double taui = m_parameters[QString("τ%1").arg(i)].value;

            // 计算相对时间并应用指数衰减
            double relativeTime = xi - x0;
            yi += Ai * exp(-relativeTime / taui);
        }

        result.append(yi);
    }

    // 如果启用了卷积并且有IRF数据，执行卷积
    if (m_useConvolution && !m_irfData.isEmpty()) {
        result = convolveWithIRF(result, m_irfData);
    }

    // 添加背景噪声B
    for (int i = 0; i < result.size(); ++i) {
        result[i] += B;
    }

    return result;
}

// 直接进行卷积拟合，
FitResult NExponentialDecayModel::fitConvolution(const QVector<double>& x, const QVector<double>& y,
                                                 double xMin, double xMax) {
    FitResult result;
    result.originalY = y;


    try {
        // 动态参数数量检查
        if(m_n < 1 || m_n > 5) {
            result.errorMessage = "指数项数量应在1-5之间";
            return result;
        }

        // 查找输入数据中的最大值及其位置
        int maxValueIndex = 0;
        double maxValue = y[0];
        for (int i = 1; i < y.size(); ++i) {
            if (y[i] > maxValue) {
                maxValue = y[i];
                maxValueIndex = i;
            }
        }

        // 输出最大值信息用于调试
        qDebug() << "最大值:" << maxValue << "位于索引:" << maxValueIndex << "对应x坐标:" << x[maxValueIndex];

        QVector<double> xFiltered, yFiltered;
        for (int i = maxValueIndex; i < xMax; ++i)
        {
            xFiltered.append(x[i] - x[maxValueIndex]);
            yFiltered.append(y[i]);
        }

        // 确保足够数据点 (每个参数至少3个点)
        if (xFiltered.size() < 3*(2*m_n +1)) {
            result.errorMessage = QString("数据点不足，至少需要%1个有效点").arg(3*(2*m_n +1));
            return result;
        }

        // 使用Eigen动态矩阵
        Eigen::VectorXd params(2*m_n +1); // 参数数量：n个A + n个τ + B

        // 1. 初始参数估计 - 改进的多指数模型初始化
        // 估计背景值B（使用最后几个点的平均值）
        int bgPoints = std::min(10, static_cast<int>(yFiltered.size() / 10));
        double baseB = 0.0;
        for (int i = yFiltered.size() - bgPoints; i < yFiltered.size(); ++i) {
            baseB += yFiltered[i];
        }
        baseB /= bgPoints;

        // 减去背景值，以便更好地估计指数衰减参数
        QVector<double> yMinusBackground;
        for (double y : yFiltered) {
            yMinusBackground.append(std::max(0.0, y - baseB));
        }


        // 从FitParameterModel获取初始参数值
        FitParameterModel* paramModel = FitParameterModel::getInstance();
        QVector<FitParameterModel::ParameterInfo> modelParams = paramModel->getParameters();

        // 创建参数名称到值的映射
        QMap<QString, double> paramValues;
        for (const auto& param : modelParams) {
            paramValues[param.name] = param.value;
        }

        // 根据指数项数量，设置初始参数
        for (int i = 1; i <= m_n; ++i) {
            QString ampName = QString("A%1").arg(i);
            QString tauName = QString("τ%1").arg(i);

            // 如果参数存在于FitParameterModel中，使用其值作为初始值
            if (paramValues.contains(ampName)) {
                params(2*(i-1)) = paramValues[ampName];
                qDebug() << "使用FitParameterModel中的值作为初始值: " << ampName << " = " << paramValues[ampName];
            } else {
                // 否则使用默认值
                params(2*(i-1)) = 100.0;
                qDebug() << "使用默认值作为初始值: " << ampName << " = 100.0";
            }

            if (paramValues.contains(tauName)) {
                params(2*(i-1)+1) = paramValues[tauName];
                qDebug() << "使用FitParameterModel中的值作为初始值: " << tauName << " = " << paramValues[tauName];
            } else {
                // 否则使用默认值，根据指数项的索引设置不同的默认值
                double defaultTau = 1.0 + i * 2.0;
                params(2*(i-1)+1) = defaultTau;
                qDebug() << "使用默认值作为初始值: " << tauName << " = " << defaultTau;
            }
        }

        // 设置背景参数B
        if (paramValues.contains("B")) {
            params(2*m_n) = paramValues["B"];
            qDebug() << "使用FitParameterModel中的值作为初始值: B = " << paramValues["B"];
        } else {
            params(2*m_n) = baseB;
            qDebug() << "使用估计的背景值作为初始值: B = " << baseB;
        }

        // 输出所有初始参数，便于调试
        qDebug() << "拟合初始参数:";
        for (int i = 0; i < m_n; ++i) {
            qDebug() << "A" << i+1 << "=" << params(2*i) << " τ" << i+1 << "=" << params(2*i+1);
        }
        qDebug() << "B=" << params(2*m_n);



        // 3. 根据选择的算法执行拟合
        qDebug() << "使用拟合算法:" << m_fitAlgorithm;

        switch (m_fitAlgorithm)
    {
        case LeastSquares:
            params = fitLeastSquares(xFiltered, yFiltered, params);
            break;
        case MaximumLikelihood:
        {
            // 先使用最小二乘法获取更好的初始参数
            //qDebug() << "卷积拟合 - 先使用最小二乘法获取初始参数，再进行最大似然估计";
            //Eigen::VectorXd lsParams = fitLeastSquares(xFiltered, yFiltered, params);
            qDebug() << "最小二乘法初始参数获取完成，开始最大似然估计";
            params = fitMaximumLikelihood(xFiltered, yFiltered, params);
            break;
        }
        case BayesianAnalysis:
        {
            // 先使用最小二乘法获取更好的初始参数
            //qDebug() << "卷积拟合 - 先使用最小二乘法获取初始参数，再进行贝叶斯分析";
            //Eigen::VectorXd lsParams = fitLeastSquares(xFiltered, yFiltered, params);
            qDebug() << "最小二乘法初始参数获取完成，开始贝叶斯分析";
            params = fitBayesian(xFiltered, yFiltered, params);
            break;
        }
        default:
            params = fitLeastSquares(xFiltered, yFiltered, params);
            break;
    }

        // 5. 更新模型参数，并强制约束tau值
        qDebug() << "N指数拟合结果(n=" << m_n << "):";

        // 更新模型参数
        for(int i=0; i<m_n; ++i) {
            qDebug() << "A" << i+1 << ":" << params(2*i) << " τ" << i+1 << ":" << params(2*i+1);
            m_parameters[QString("A%1").arg(i+1)].value = std::max(0.0, params(2*i));
            // 确保tau值在合理范围内
            double tau = params(2*i+1);
            if (m_n >= 3) {
                tau = std::min(10.0, tau); // 三指数及以上模型强制限制tau不超过10
            }
            m_parameters[QString("τ%1").arg(i+1)].value = std::max(0.1, tau);
        }
        qDebug() << "B:" << params(2*m_n);
        m_parameters["B"].value = params(2*m_n);

        // 6. 生成拟合曲线，只绘制参与拟合的点的曲线
        // 创建一个新的x数据向量，只包含从maxIndex到endIndex的点（实际参与拟合的范围）
        QVector<double> fitX;
        for (int i = xMin; i < xMax; ++i) {
            fitX.append(x[i]);
        }

        // 计算对应的拟合曲线
        result.xData = fitX;
        result.yData = calculate(fitX);

        // 7. 计算拟合指标
        // 为了正确计算拟合指标，需要提取与yFiltered对应的拟合值
        QVector<double> fittedValues;
        for (int i = 0; i < yFiltered.size(); ++i) {
            // 计算拟合曲线在对应点的值
            double t = xFiltered[i]; // 这是相对时间
            double fittedValue = m_parameters["B"].value;
            for (int j = 1; j <= m_n; ++j) {
                double Aj = m_parameters[QString("A%1").arg(j)].value;
                double tauj = m_parameters[QString("τ%1").arg(j)].value;
                fittedValue += Aj * exp(-t / tauj);
            }
            fittedValues.append(fittedValue);
        }

        // 使用实际参与拟合的点计算拟合指标
        result.chiSquare = calculateChiSquare(yFiltered, fittedValues);
        result.rSquare = calculateRSquared(yFiltered, fittedValues);

        // 保存使用的算法信息
        result.parameters["FitAlgorithm"] = static_cast<double>(m_fitAlgorithm);

        // 保存迭代次数和容差信息
        result.parameters["Iterations"] = static_cast<double>(FitParameterModel::getInstance()->iterations());
        result.parameters["Tolerance"] = FitParameterModel::getInstance()->tolerance();

        // 保存参数
        for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
            result.parameters[it.key()] = it.value().value;
        }

        result.success = true;
    } catch (const std::exception& e) {
        result.errorMessage = QString("拟合错误: %1").arg(e.what());
        qDebug() << "N指数拟合异常:" << e.what();
    }

    return result;
}



FitResult NExponentialDecayModel::fit(const QVector<double>& x, const QVector<double>& y,
                                      double xMin, double xMax) {

    FitResult result;
    result.originalY = y;

    qDebug() << "执行尾部拟合，范围:" << xMin << "到" << xMax;

    // 保存当前卷积状态
    bool originalConvolutionState = m_useConvolution;
    QVector<double> originalIrfData = m_irfData;

    // 临时禁用卷积，确保尾部拟合不受卷积影响
    m_useConvolution = false;

    try {
        // 动态参数数量检查
        if(m_n < 1 || m_n > 5) {
            result.errorMessage = "指数项数量应在1-5之间";
            // 恢复卷积状态
            m_useConvolution = originalConvolutionState;
            m_irfData = originalIrfData;
            return result;
        }

        QVector<double> xFiltered, yFiltered;
        for (int i = xMin; i < xMax; ++i)
        {
            xFiltered.append(x[i] - x[xMin]);
            yFiltered.append(y[i]);
        }

        // 确保足够数据点 (每个参数至少3个点)
        if (xFiltered.size() < 3*(2*m_n +1)) {
            result.errorMessage = QString("数据点不足，至少需要%1个有效点").arg(3*(2*m_n +1));
            return result;
        }

        // 使用Eigen动态矩阵
        Eigen::VectorXd params(2*m_n +1); // 参数数量：n个A + n个τ + B


        // 从FitParameterModel获取初始参数值
        FitParameterModel* paramModel = FitParameterModel::getInstance();
        QVector<FitParameterModel::ParameterInfo> modelParams = paramModel->getParameters();

        // 创建参数名称到值的映射
        QMap<QString, double> paramValues;
        for (const auto& param : modelParams) {
            paramValues[param.name] = param.value;
        }

        // 根据指数项数量，设置初始参数
        for (int i = 1; i <= m_n; ++i) {
            QString ampName = QString("A%1").arg(i);
            QString tauName = QString("τ%1").arg(i);

            // 如果参数存在于FitParameterModel中，使用其值作为初始值
            if (paramValues.contains(ampName)) {
                params(2*(i-1)) = paramValues[ampName];
                qDebug() << "使用FitParameterModel中的值作为初始值: " << ampName << " = " << paramValues[ampName];
            } else {
                // 否则使用默认值
                params(2*(i-1)) = 100.0;
                qDebug() << "使用默认值作为初始值: " << ampName << " = 100.0";
            }

            if (paramValues.contains(tauName)) {
                params(2*(i-1)+1) = paramValues[tauName];
                qDebug() << "使用FitParameterModel中的值作为初始值: " << tauName << " = " << paramValues[tauName];
            } else {
                // 否则使用默认值，根据指数项的索引设置不同的默认值
                double defaultTau = 1.0 + i * 2.0;
                params(2*(i-1)+1) = defaultTau;
                qDebug() << "使用默认值作为初始值: " << tauName << " = " << defaultTau;
            }
        }

        // 设置背景参数B
        if (paramValues.contains("B")) {
            params(2*m_n) = paramValues["B"];
            qDebug() << "使用FitParameterModel中的值作为初始值: B = " << paramValues["B"];
        } else {
            params(2*m_n) = 0.0; // 不估计背景噪声，默认设为0
            qDebug() << "不估计背景噪声，使用默认值作为初始值: B = 0.0";
        }

        // 输出所有初始参数，便于调试
        qDebug() << "拟合初始参数:";
        for (int i = 0; i < m_n; ++i) {
            qDebug() << "A" << i+1 << "=" << params(2*i) << " τ" << i+1 << "=" << params(2*i+1);
        }
        qDebug() << "B=" << params(2*m_n);


        // 3. 根据选择的算法执行拟合
        qDebug() << "使用拟合算法:" << m_fitAlgorithm;

        switch (m_fitAlgorithm)
        {
        case LeastSquares:
            params = fitLeastSquares(xFiltered, yFiltered, params);
            break;

        case MaximumLikelihood:
        {
            // 先使用最小二乘法获取更好的初始参数
            //qDebug() << "尾部拟合 - 先使用最小二乘法获取初始参数，再进行最大似然估计";
            //Eigen::VectorXd lsParams = fitLeastSquares(xFiltered, yFiltered, params);
            qDebug() << "最小二乘法初始参数获取完成，开始最大似然估计";
            params = fitMaximumLikelihood(xFiltered, yFiltered, params);
            break;
        }

        case BayesianAnalysis:
        {
            // 先使用最小二乘法获取更好的初始参数
            qDebug() << "尾部拟合 - 先使用最小二乘法获取初始参数，再进行贝叶斯分析";
            //Eigen::VectorXd lsParams = fitLeastSquares(xFiltered, yFiltered, params);
            qDebug() << "最小二乘法初始参数获取完成，开始贝叶斯分析";
            params = fitBayesian(xFiltered, yFiltered, params);
            break;
        }

        default:
            params = fitLeastSquares(xFiltered, yFiltered, params);
            break;
        }


        // 5. 更新模型参数，并强制约束tau值
        qDebug() << "N指数拟合结果(n=" << m_n << "):";

        // 对于三指数及以上模型，检查tau值是否合理
        // if (m_n >= 3)
        // {
        //     // 首先检查是否有tau值超过合理范围
        //     double maxAllowedTau = 9.0; // 允许的最大tau值，稍微放宽一点
        //     bool needsAdjustment = false;

        //     // 检查tau值是否合理
        //     for(int i=0; i<m_n; ++i) {
        //         if (params(2*i+1) > maxAllowedTau) {
        //             needsAdjustment = true;
        //             qDebug() << "警告: tau" << i+1 << "=" << params(2*i+1) << "超过" << maxAllowedTau << "，需要调整";
        //         }
        //     }

        //     // 检查tau值是否有足够的间隔
        //     for(int i=1; i<m_n; ++i) {
        //         int idx1 = -1, idx2 = -1;
        //         double minTau = std::numeric_limits<double>::max();
        //         double secondMinTau = std::numeric_limits<double>::max();

        //         // 找出最小的两个tau值
        //         for(int j=0; j<m_n; ++j) {
        //             double tau = params(2*j+1);
        //             if (tau < minTau) {
        //                 secondMinTau = minTau;
        //                 idx2 = idx1;
        //                 minTau = tau;
        //                 idx1 = j;
        //             } else if (tau < secondMinTau) {
        //                 secondMinTau = tau;
        //                 idx2 = j;
        //             }
        //         }

        //         // 如果两个tau值太接近，标记需要调整
        //         if (idx1 >= 0 && idx2 >= 0) {
        //             double ratio = secondMinTau / minTau;
        //             if (ratio < 1.2) { // 如果比值小于1.2，认为太接近
        //                 needsAdjustment = true;
        //                 qDebug() << "警告: tau" << idx1+1 << "=" << minTau << "和tau" << idx2+1 << "=" << secondMinTau << "太接近，需要调整";
        //             }
        //         }
        //     }

        //     // 如果需要调整，尝试多种调整策略
        //     if (needsAdjustment) {
        //         qDebug() << "尝试调整tau值...";

        //         // 策略1: 使用FitParameterModel中的值或固定的tau值分布
        //         Eigen::VectorXd adjustedParams1 = params;
        //         // 从FitParameterModel获取初始参数值
        //         FitParameterModel* paramModel = FitParameterModel::getInstance();
        //         QVector<FitParameterModel::ParameterInfo> modelParams = paramModel->getParameters();
        //         QMap<QString, double> paramValues;
        //         for (const auto& param : modelParams) {
        //             paramValues[param.name] = param.value;
        //         }

        //         // 对于三指数模型，优先使用FitParameterModel中的值，否则使用2ns, 4ns, 6ns的固定分布
        //         if (m_n == 3) {
        //             // 设置tau值
        //             if (paramValues.contains("τ1")) adjustedParams1(1) = paramValues["τ1"];
        //             else adjustedParams1(1) = 2.0; // tau1

        //             if (paramValues.contains("τ2")) adjustedParams1(3) = paramValues["τ2"];
        //             else adjustedParams1(3) = 4.0; // tau2

        //             if (paramValues.contains("τ3")) adjustedParams1(5) = paramValues["τ3"];
        //             else adjustedParams1(5) = 6.0; // tau3

        //             // 设置振幅值
        //             if (paramValues.contains("A1")) adjustedParams1(0) = paramValues["A1"];
        //             if (paramValues.contains("A2")) adjustedParams1(2) = paramValues["A2"];
        //             if (paramValues.contains("A3")) adjustedParams1(4) = paramValues["A3"];

        //             // 设置背景噪声B
        //             if (paramValues.contains("B")) adjustedParams1(2*m_n) = paramValues["B"];

        //             qDebug() << "策略1: 使用FitParameterModel中的值或默认值 - tau1=" << adjustedParams1(1)
        //                      << " tau2=" << adjustedParams1(3) << " tau3=" << adjustedParams1(5)
        //                      << " B=" << adjustedParams1(2*m_n);
        //         }

        //         // 策略2: 基于当前tau值的比例调整
        //         Eigen::VectorXd adjustedParams2 = params;
        //         // 按照tau值从小到大排序
        //         std::vector<std::pair<int, double>> tauValues;
        //         for(int i=0; i<m_n; ++i) {
        //             tauValues.push_back(std::make_pair(i, params(2*i+1)));
        //         }
        //         std::sort(tauValues.begin(), tauValues.end(),
        //                   [](const std::pair<int, double>& a, const std::pair<int, double>& b) {
        //                       return a.second < b.second;
        //                   });

        //         // 对于三指数模型，使用基于最小tau值的比例调整
        //         if (m_n == 3) {
        //             double minTau = tauValues[0].second;
        //             // 确保minTau在合理范围内
        //             minTau = std::max(1.0, std::min(3.0, minTau));

        //             // 设置tau值为minTau, 2*minTau, 3.5*minTau
        //             for(int i=0; i<m_n; ++i) {
        //                 int idx = tauValues[i].first;
        //                 if (i == 0) {
        //                     adjustedParams2(2*idx+1) = minTau;
        //                 } else if (i == 1) {
        //                     adjustedParams2(2*idx+1) = std::min(maxAllowedTau, 2.0 * minTau);
        //                 } else {
        //                     adjustedParams2(2*idx+1) = std::min(maxAllowedTau, 3.5 * minTau);
        //                 }
        //             }

        //             // 设置振幅值 - 保持原始振幅

        //             // 设置背景噪声B - 优先使用FitParameterModel中的值
        //             if (paramValues.contains("B")) {
        //                 adjustedParams2(2*m_n) = paramValues["B"];
        //                 qDebug() << "策略2: 使用FitParameterModel中的B值: " << paramValues["B"];
        //             }
        //         }

        //         // 计算原始参数和两种调整策略的拟合质量
        //         QVector<double> fittedValues, fittedValues1, fittedValues2;
        //         for (int i = 0; i < xFiltered.size(); i++) {
        //             // 原始参数
        //             double sum = params(2*m_n);
        //             for(int j=0; j<m_n; ++j) {
        //                 double A = params(2*j);
        //                 double tau = params(2*j+1);
        //                 sum += A * exp(-xFiltered[i]/tau);
        //             }
        //             fittedValues.append(sum);

        //             // 策略1
        //             double sum1 = adjustedParams1(2*m_n);
        //             for(int j=0; j<m_n; ++j) {
        //                 double A = adjustedParams1(2*j);
        //                 double tau = adjustedParams1(2*j+1);
        //                 sum1 += A * exp(-xFiltered[i]/tau);
        //             }
        //             fittedValues1.append(sum1);

        //             // 策略2
        //             double sum2 = adjustedParams2(2*m_n);
        //             for(int j=0; j<m_n; ++j) {
        //                 double A = adjustedParams2(2*j);
        //                 double tau = adjustedParams2(2*j+1);
        //                 sum2 += A * exp(-xFiltered[i]/tau);
        //             }
        //             fittedValues2.append(sum2);
        //         }

        //         // 计算对数似然值
        //         double logLikelihood = calculateLogLikelihood(yFiltered, fittedValues);
        //         double logLikelihood1 = calculateLogLikelihood(yFiltered, fittedValues1);
        //         double logLikelihood2 = calculateLogLikelihood(yFiltered, fittedValues2);

        //         qDebug() << "原始参数对数似然值:" << logLikelihood;
        //         qDebug() << "策略1对数似然值:" << logLikelihood1;
        //         qDebug() << "策略2对数似然值:" << logLikelihood2;

        //         // 选择最佳策略
        //         if (logLikelihood1 > logLikelihood && logLikelihood1 > logLikelihood2) {
        //             params = adjustedParams1;
        //             qDebug() << "选择策略1的tau值分布";
        //         } else if (logLikelihood2 > logLikelihood && logLikelihood2 > logLikelihood1) {
        //             params = adjustedParams2;
        //             qDebug() << "选择策略2的tau值分布";
        //         } else {
        //             // 如果原始参数最好，但有tau值超过maxAllowedTau，则截断
        //             for(int i=0; i<m_n; ++i) {
        //                 if (params(2*i+1) > maxAllowedTau) {
        //                     params(2*i+1) = maxAllowedTau;
        //                 }
        //             }
        //             qDebug() << "保留原始参数，但截断超过" << maxAllowedTau << "的tau值";
        //         }

        //         qDebug() << "调整后的tau值:";
        //         for(int i=0; i<m_n; ++i) {
        //             qDebug() << "tau" << i+1 << "=" << params(2*i+1);
        //         }
        //     }
        // }

        // 更新模型参数
        for(int i=0; i<m_n; ++i) {
            qDebug() << "A" << i+1 << ":" << params(2*i) << " τ" << i+1 << ":" << params(2*i+1);
            m_parameters[QString("A%1").arg(i+1)].value = std::max(0.0, params(2*i));
            // 确保tau值在合理范围内
            double tau = params(2*i+1);
            if (m_n >= 3) {
                tau = std::min(10.0, tau); // 三指数及以上模型强制限制tau不超过10
            }
            m_parameters[QString("τ%1").arg(i+1)].value = std::max(0.1, tau);
        }
        qDebug() << "B:" << params(2*m_n);
        m_parameters["B"].value = params(2*m_n);

        // 6. 生成拟合曲线，只绘制参与拟合的点的曲线
        // 创建一个新的x数据向量，只包含从maxIndex到endIndex的点（实际参与拟合的范围）
        QVector<double> fitX;
        for (int i = xMin; i < xMax; ++i) {
            fitX.append(x[i]);
        }

        // 计算对应的拟合曲线
        result.xData = fitX;
        result.yData = calculate(fitX);

        // 7. 计算拟合指标
        // 为了正确计算拟合指标，需要提取与yFiltered对应的拟合值
        QVector<double> fittedValues;
        for (int i = 0; i < yFiltered.size(); ++i) {
            // 计算拟合曲线在对应点的值
            double t = xFiltered[i]; // 这是相对时间
            double fittedValue = m_parameters["B"].value;
            for (int j = 1; j <= m_n; ++j) {
                double Aj = m_parameters[QString("A%1").arg(j)].value;
                double tauj = m_parameters[QString("τ%1").arg(j)].value;
                fittedValue += Aj * exp(-t / tauj);
            }
            fittedValues.append(fittedValue);
        }

        // 使用实际参与拟合的点计算拟合指标
        result.chiSquare = calculateChiSquare(yFiltered, fittedValues);
        result.rSquare = calculateRSquared(yFiltered, fittedValues);

        // 保存使用的算法信息
        result.parameters["FitAlgorithm"] = static_cast<double>(m_fitAlgorithm);

        // 保存迭代次数和容差信息
        result.parameters["Iterations"] = static_cast<double>(FitParameterModel::getInstance()->iterations());
        result.parameters["Tolerance"] = FitParameterModel::getInstance()->tolerance();

        // 根据使用的算法保存相应的信息
        double logLikelihood = 0.0;

        switch (m_fitAlgorithm) {
        case LeastSquares:
        {
            result.parameters["FitMethod"] = 1.0; // Least Square Method
            qDebug() << "Using Least Square Method";
            break;
        }

        case MaximumLikelihood:
        {
            // 计算并保存对数似然值
            logLikelihood = calculateLogLikelihood(yFiltered, fittedValues);
            result.parameters["LogLikelihood"] = logLikelihood;
            result.parameters["FitMethod"] = 2.0; // Maximum Likelihood Estimation
            qDebug() << "Using Maximum Likelihood Estimation";
            qDebug() << "Log Likelihood:" << logLikelihood;
            break;
        }

        case BayesianAnalysis:
        {
            // 计算并保存对数似然值
            logLikelihood = calculateLogLikelihood(yFiltered, fittedValues);
            result.parameters["LogLikelihood"] = logLikelihood;
            result.parameters["FitMethod"] = 3.0; // Bayesian Analysis
            result.parameters["BayesianAnalysis"] = 1.0; // 标记使用了贝叶斯分析

            qDebug() << "Using Bayesian Analysis";
            qDebug() << "Log Likelihood:" << logLikelihood;
            qDebug() << "Bayesian Analysis Parameter Standard Deviations:";
            for (int i = 1; i <= m_n; i++) {
                qDebug() << "  A" << i << "_std:" << result.parameters[QString("A%1_std").arg(i)];
                qDebug() << "  τ" << i << "_std:" << result.parameters[QString("τ%1_std").arg(i)];
            }
            qDebug() << "  B_std:" << result.parameters["B_std"];
            break;
        }

        default:
        {
            result.parameters["FitMethod"] = 1.0; // Default to Least Square Method
            qDebug() << "Using default Least Square Method";
            break;
        }
        }

        // 保存参数
        for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
            result.parameters[it.key()] = it.value().value;
        }

        result.success = true;
    } catch (const std::exception& e) {
        result.errorMessage = QString("拟合错误: %1").arg(e.what());
        qDebug() << "N指数拟合异常:" << e.what();
    }

    // 恢复卷积状态
    m_useConvolution = originalConvolutionState;
    m_irfData = originalIrfData;
    qDebug() << "尾部拟合完成，恢复卷积状态:" << (m_useConvolution ? "启用" : "禁用");

    return result;
}



// 高斯模型实现
GaussianModel::GaussianModel() {
    // 初始化参数
    m_parameters["A"] = FitParameter("A", 1000.0, 0.0, 1000000.0);
    m_parameters["μ"] = FitParameter("μ", 500.0, 0.0, 1000.0);
    m_parameters["σ"] = FitParameter("σ", 50.0, 0.1, 1000.0);
    m_parameters["B"] = FitParameter("B", 0.0, -1000.0, 1000.0);
}

QVector<FitParameter> GaussianModel::getParameters() const {
    QVector<FitParameter> params;
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        params.append(it.value());
    }
    return params;
}

void GaussianModel::setParameter(const QString& name, double value) {
    if (m_parameters.contains(name)) {
        m_parameters[name].value = value;
    }
}

void GaussianModel::setParameterFixed(const QString& name, bool fixed) {
    if (m_parameters.contains(name)) {
        m_parameters[name].fixed = fixed;
    }
}

void GaussianModel::setParameterRange(const QString& name, double min, double max) {
    if (m_parameters.contains(name)) {
        m_parameters[name].minValue = min;
        m_parameters[name].maxValue = max;
    }
}

QVector<double> GaussianModel::calculate(const QVector<double>& x) {
    QVector<double> result;
    double A = m_parameters["A"].value;
    double mu = m_parameters["μ"].value;
    double sigma = m_parameters["σ"].value;
    double B = m_parameters["B"].value;

    for (double xi : x) {
        double exponent = -pow(xi - mu, 2) / (2 * pow(sigma, 2));
        double yi = A * exp(exponent) + B;
        result.append(yi);
    }

    return result;
}


FitResult GaussianModel::fit(const QVector<double>& x, const QVector<double>& y,
                             double xMin, double xMax) {
    FitResult result;
    // 先保存原始数据，但不立即设置 result.xData
    result.originalY = y;

    try {
        // 筛选数据范围
        QVector<double> xFiltered, yFiltered;
        if (xMin != xMax) {
            for (int i = 0; i < x.size(); ++i) {
                if (x[i] >= xMin && x[i] <= xMax) {
                    xFiltered.append(x[i]);
                    yFiltered.append(y[i]);
                }
            }
        } else {
            xFiltered = x;
            yFiltered = y;
        }

        if (xFiltered.isEmpty()) {
            result.errorMessage = "没有在指定范围内的数据点";
            return result;
        }

        // 估计初始参数
        // 找到最大值点作为μ的初始估计
        int maxIndex = 0;
        double maxY = yFiltered[0];
        for (int i = 1; i < yFiltered.size(); ++i) {
            if (yFiltered[i] > maxY) {
                maxY = yFiltered[i];
                maxIndex = i;
            }
        }

        // 估计背景值B
        int numPointsForB = std::min(10, static_cast<int>(xFiltered.size()));
        double B = 0;
        for (int i = 0; i < numPointsForB; ++i) {
            B += yFiltered[i];
        }
        for (int i = xFiltered.size() - numPointsForB; i < xFiltered.size(); ++i) {
            B += yFiltered[i];
        }
        B /= (2 * numPointsForB);

        // 设置初始参数
        double mu = xFiltered[maxIndex];
        double A = maxY - B;

        // 估计σ：找到半高宽
        double halfMax = B + A / 2;
        int leftIndex = maxIndex;
        while (leftIndex > 0 && yFiltered[leftIndex] > halfMax) {
            leftIndex--;
        }

        int rightIndex = maxIndex;
        while (rightIndex < yFiltered.size() - 1 && yFiltered[rightIndex] > halfMax) {
            rightIndex++;
        }

        double sigma = (xFiltered[rightIndex] - xFiltered[leftIndex]) / 2.355; // FWHM = 2.355 * sigma

        // 更新参数
        m_parameters["A"].value = A;
        m_parameters["μ"].value = mu;
        m_parameters["σ"].value = sigma;
        m_parameters["B"].value = B;

        // 使用原始数据的 x 范围生成拟合曲线，而不仅仅是过滤后的范围
        // 这样可以确保拟合曲线覆盖整个数据范围
        if (!x.isEmpty()) {
            // 使用原始数据的 x 值作为拟合曲线的 x 值
            result.xData = x;

            // 使用拟合参数计算对应的 y 值
            result.yData = calculate(x);
        } else {
            // 如果原始数据为空，则使用过滤后的数据
            result.xData = xFiltered;
            result.yData = calculate(xFiltered);
        }

        // 确保 xData 和 yData 大小相同
        if (result.xData.size() != result.yData.size()) {
            qWarning() << "GaussianModel::fit - xData and yData size mismatch, adjusting";
            // 如果大小不同，使用较小的大小
            int minSize = qMin(result.xData.size(), result.yData.size());
            if (minSize > 0) {
                result.xData = result.xData.mid(0, minSize);
                result.yData = result.yData.mid(0, minSize);
            }
        }

        // 计算拟合优度
        result.chiSquare = calculateChiSquare(yFiltered, result.yData);
        result.rSquare = calculateRSquared(yFiltered, result.yData);

        // 保存参数
        for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
            result.parameters[it.key()] = it.value().value;
        }

        result.success = true;
    } catch (const std::exception& e) {
        result.errorMessage = QString("拟合过程中发生错误: %1").arg(e.what());
        qDebug() << "拟合错误:" << e.what();
    } catch (...) {
        result.errorMessage = "拟合过程中发生未知错误";
        qDebug() << "拟合过程中发生未知错误";
    }

    return result;
}


// 洛伦兹模型实现
LorentzianModel::LorentzianModel() {
    // 初始化参数
    m_parameters["A"] = FitParameter("A", 1000.0, 0.0, 1000000.0);
    m_parameters["x₀"] = FitParameter("x₀", 500.0, 0.0, 1000.0);
    m_parameters["γ"] = FitParameter("γ", 50.0, 0.1, 1000.0);
    m_parameters["B"] = FitParameter("B", 0.0, -1000.0, 1000.0);
}

QVector<FitParameter> LorentzianModel::getParameters() const {
    QVector<FitParameter> params;
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        params.append(it.value());
    }
    return params;
}

void LorentzianModel::setParameter(const QString& name, double value) {
    if (m_parameters.contains(name)) {
        m_parameters[name].value = value;
    }
}

void LorentzianModel::setParameterFixed(const QString& name, bool fixed) {
    if (m_parameters.contains(name)) {
        m_parameters[name].fixed = fixed;
    }
}

void LorentzianModel::setParameterRange(const QString& name, double min, double max) {
    if (m_parameters.contains(name)) {
        m_parameters[name].minValue = min;
        m_parameters[name].maxValue = max;
    }
}

QVector<double> LorentzianModel::calculate(const QVector<double>& x) {
    QVector<double> result;
    double A = m_parameters["A"].value;
    double x0 = m_parameters["x₀"].value;
    double gamma = m_parameters["γ"].value;
    double B = m_parameters["B"].value;

    for (double xi : x) {
        double yi = A * pow(gamma, 2) / (pow(xi - x0, 2) + pow(gamma, 2)) + B;
        result.append(yi);
    }

    return result;
}


FitResult LorentzianModel::fit(const QVector<double>& x, const QVector<double>& y,
                               double xMin, double xMax) {
    FitResult result;
    // 先保存原始数据，但不立即设置 result.xData
    result.originalY = y;

    try {
        // 筛选数据范围
        QVector<double> xFiltered, yFiltered;
        if (xMin != xMax) {
            for (int i = 0; i < x.size(); ++i) {
                if (x[i] >= xMin && x[i] <= xMax) {
                    xFiltered.append(x[i]);
                    yFiltered.append(y[i]);
                }
            }
        } else {
            xFiltered = x;
            yFiltered = y;
        }

        if (xFiltered.isEmpty()) {
            result.errorMessage = "没有在指定范围内的数据点";
            return result;
        }

        // 估计初始参数
        // 找到最大值点作为x₀的初始估计
        int maxIndex = 0;
        double maxY = yFiltered[0];
        for (int i = 1; i < yFiltered.size(); ++i) {
            if (yFiltered[i] > maxY) {
                maxY = yFiltered[i];
                maxIndex = i;
            }
        }

        // 估计背景值B
        int numPointsForB = std::min(10, static_cast<int>(xFiltered.size()));
        double B = 0;
        for (int i = 0; i < numPointsForB; ++i) {
            B += yFiltered[i];
        }
        for (int i = xFiltered.size() - numPointsForB; i < xFiltered.size(); ++i) {
            B += yFiltered[i];
        }
        B /= (2 * numPointsForB);

        // 设置初始参数
        double x0 = xFiltered[maxIndex];
        double A = maxY - B;

        // 估计γ：找到半高宽
        double halfMax = B + A / 2;
        int leftIndex = maxIndex;
        while (leftIndex > 0 && yFiltered[leftIndex] > halfMax) {
            leftIndex--;
        }

        int rightIndex = maxIndex;
        while (rightIndex < yFiltered.size() - 1 && yFiltered[rightIndex] > halfMax) {
            rightIndex++;
        }

        double gamma = (xFiltered[rightIndex] - xFiltered[leftIndex]) / 2.0; // FWHM = 2 * gamma

        // 更新参数
        m_parameters["A"].value = A;
        m_parameters["x₀"].value = x0;
        m_parameters["γ"].value = gamma;
        m_parameters["B"].value = B;

        // 使用原始数据的 x 范围生成拟合曲线，而不仅仅是过滤后的范围
        // 这样可以确保拟合曲线覆盖整个数据范围
        if (!x.isEmpty()) {
            // 使用原始数据的 x 值作为拟合曲线的 x 值
            result.xData = x;

            // 使用拟合参数计算对应的 y 值
            result.yData = calculate(x);
        } else {
            // 如果原始数据为空，则使用过滤后的数据
            result.xData = xFiltered;
            result.yData = calculate(xFiltered);
        }

        // 确保 xData 和 yData 大小相同
        if (result.xData.size() != result.yData.size()) {
            qWarning() << "LorentzianModel::fit - xData and yData size mismatch, adjusting";
            // 如果大小不同，使用较小的大小
            int minSize = qMin(result.xData.size(), result.yData.size());
            if (minSize > 0) {
                result.xData = result.xData.mid(0, minSize);
                result.yData = result.yData.mid(0, minSize);
            }
        }

        // 计算拟合优度
        result.chiSquare = calculateChiSquare(yFiltered, result.yData);
        result.rSquare = calculateRSquared(yFiltered, result.yData);

        // 保存参数
        for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
            result.parameters[it.key()] = it.value().value;
        }

        result.success = true;
    } catch (const std::exception& e) {
        result.errorMessage = QString("拟合过程中发生错误: %1").arg(e.what());
        qDebug() << "拟合错误:" << e.what();
    } catch (...) {
        result.errorMessage = "拟合过程中发生未知错误";
        qDebug() << "拟合过程中发生未知错误";
    }

    return result;
}
