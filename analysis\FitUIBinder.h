#ifndef FITUIBINDER_H
#define FITUIBINDER_H

#include <QObject>
#include <QComboBox>
#include <QDoubleSpinBox>
#include <QSpinBox>
#include <QTableWidget>
#include <QPushButton>
#include <QCheckBox>
#include "FitParameterModel.h"

// UI绑定类，用于将UI控件与FitParameterModel绑定
class FitUIBinder : public QObject {
    Q_OBJECT

public:
    explicit FitUIBinder(QObject* parent = nullptr);
    ~FitUIBinder();

    // 绑定UI控件
    void bindAnalysisMethod(QComboBox* comboBox);
    void bindFittingModel(QComboBox* comboBox);
    void bindIRFParameters(QDoubleSpinBox* t0SpinBox, QDoubleSpinBox* fwhmSpinBox);
    void bindAnalysisMode(QComboBox* comboBox);
    void bindExponentialModel(QComboBox* comboBox);
    void bindModelParametersCount(QSpinBox* spinBox);
    void bindModelAlgorithm(QComboBox* comboBox);
    void bindIterations(QSpinBox* spinBox);
    void bindTolerance(QDoubleSpinBox* spinBox);
    void bindFittingRange(QDoubleSpinBox* fromSpinBox, QDoubleSpinBox* toSpinBox);
    void bindParameterTable(QTableWidget* tableWidget);

    // 更新参数表格
    void updateParameterTable();

private slots:
    // UI控件值变化的槽函数
    void onAnalysisMethodChanged(const QString& text);
    void onFittingModelChanged(const QString& text);
    void onT0Changed(double value);
    void onFwhmChanged(double value);
    void onAnalysisModeChanged(const QString& text);
    void onExponentialModelChanged(const QString& text);
    void onModelParametersCountChanged(int value);
    void onModelAlgorithmChanged(const QString& text);
    void onIterationsChanged(int value);
    void onToleranceChanged(double value);
    void onRangeFromChanged(double value);
    void onRangeToChanged(double value);

    // 参数表格相关槽函数
    void onParameterValueChanged(double value);
    void onParameterFixedChanged(bool checked);
    void onParameterLimitsClicked();

    // 模型参数变化的槽函数
    void onModelParametersChanged();
    void onModelParameterChanged(const QString& name);

private:
    // 创建参数表格行
    void setupParameterRow(int row, const FitParameterModel::ParameterInfo& param);

    // 获取参数表格中的参数名称
    QString getParameterNameFromRow(int row) const;

    // UI控件
    QComboBox* m_analysisMethodComboBox = nullptr;
    QComboBox* m_fittingModelComboBox = nullptr;
    QDoubleSpinBox* m_t0SpinBox = nullptr;
    QDoubleSpinBox* m_fwhmSpinBox = nullptr;
    QComboBox* m_analysisModeComboBox = nullptr;
    QComboBox* m_exponentialModelComboBox = nullptr;
    QSpinBox* m_modelParamsSpinBox = nullptr;
    QComboBox* m_modelAlgorithmComboBox = nullptr;
    QSpinBox* m_iterationsSpinBox = nullptr;
    QDoubleSpinBox* m_toleranceSpinBox = nullptr;
    QDoubleSpinBox* m_rangeFromSpinBox = nullptr;
    QDoubleSpinBox* m_rangeToSpinBox = nullptr;
    QTableWidget* m_parameterTable = nullptr;

    // 参数模型
    FitParameterModel* m_model;

    // 标记是否正在更新UI，避免循环更新
    bool m_updatingUI = false;
};

#endif // FITUIBINDER_H
